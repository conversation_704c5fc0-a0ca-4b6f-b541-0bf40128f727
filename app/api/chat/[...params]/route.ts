import { NextRequest } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { params: string[] } }
) {
  try {
    const urlParams = params.params;
    console.log('Chat API dynamic GET params:', urlParams);

    // Handle comma-separated parameters in the URL
    // The AI SDK seems to be making requests like: /api/chat,{id},status
    const fullPath = urlParams.join(',');
    console.log('Full path:', fullPath);

    // Parse the comma-separated format
    const parts = fullPath.split(',');
    if (parts.length >= 2) {
      const [chatId, action] = parts;
      
      console.log('Parsed - chatId:', chatId, 'action:', action);

      // Handle different actions that the AI SDK expects
      switch (action) {
        case 'error':
          return new Response('', { status: 200 });
          
        case 'status':
          return Response.json({ status: 'ready' });
          
        case 'streamData':
          return new Response('', { status: 200 });
          
        default:
          console.log('Unknown action:', action);
          return new Response('', { status: 200 });
      }
    }

    // Default response for any other format
    return new Response('', { status: 200 });
  } catch (error) {
    console.error('Chat dynamic API GET error:', error);
    return new Response('', { status: 200 }); // Return 200 to avoid errors
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { params: string[] } }
) {
  try {
    const urlParams = params.params;
    console.log('Chat API dynamic POST params:', urlParams);

    // For POST requests, just return success
    return new Response('', { status: 200 });
  } catch (error) {
    console.error('Chat dynamic API POST error:', error);
    return new Response('', { status: 200 });
  }
}
