"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Chat } from "../components/chat"
import { useAuth } from "@/components/auth-provider"

export default function ChatPage() {
  const { sessionId } = useParams()
  const { user } = useAuth()

  // TODO: Fetch initial messages for this session from Supabase
  const [initialMessages, setInitialMessages] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: Load chat session data from Supabase
    // For now, just set loading to false
    setIsLoading(false)
  }, [sessionId])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Loading chat...</h2>
          <p className="text-muted-foreground">Please wait while we load your conversation.</p>
        </div>
      </div>
    )
  }

  return (
    <Chat
      id={sessionId as string}
      initialMessages={initialMessages}
      initialChatModel="openai/gpt-4o"
      initialVisibilityType="private"
      isReadonly={false}
      autoResume={false}
    />
  )
}
