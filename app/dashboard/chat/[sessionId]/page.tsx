"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Chat } from "../components/chat"
import { useAuth } from "@/components/auth-provider"

export default function ChatPage() {
  const { sessionId } = useParams()
  const { user } = useAuth()

  const [initialMessages, setInitialMessages] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadChatSession = async () => {
      if (!user?.id || !sessionId) {
        setIsLoading(false)
        return
      }

      try {
        // Load chat messages for this session
        const response = await fetch(`/api/chat/messages?sessionId=${sessionId}`, {
          headers: {
            'x-user-id': user.id,
          },
        });

        if (response.ok) {
          const { messages } = await response.json();
          setInitialMessages(messages || []);
        } else {
          console.log('No existing messages for session:', sessionId);
          setInitialMessages([]);
        }
      } catch (error) {
        console.error('Error loading chat session:', error);
        setInitialMessages([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadChatSession();
  }, [sessionId, user]);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Authentication required</h2>
          <p className="text-muted-foreground">Please sign in to access this chat.</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Loading chat...</h2>
          <p className="text-muted-foreground">Please wait while we load your conversation.</p>
        </div>
      </div>
    )
  }

  return (
    <Chat
      id={sessionId as string}
      initialMessages={initialMessages}
      initialChatModel="google/gemini-2.5-flash-preview"
      initialVisibilityType="private"
      isReadonly={false}
      autoResume={false}
    />
  )
}
