import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { ChatSDKError } from '../../lib/errors';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('API /dashboard/chat/api/chat: Received request body:', body);

    const { messages, id, selectedChatModel, selectedVisibilityType } = body;

    // Get user ID from header (for Supabase auth)
    const userId = request.headers.get("x-user-id");

    if (!userId) {
      console.log('API: No user ID in headers');
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    // Get the latest user message
    const userMessage = messages[messages.length - 1];
    console.log('API: Processing message:', userMessage?.content?.slice(0, 100) + '...');

    // TODO: Validate chat access and load chat history from Supabase
    // TODO: Save user message to Supabase

    const result = await streamText({
      model: openai(selectedChatModel || 'gpt-4o'),
      messages: messages, // Use all messages for context
      onFinish: async ({ text }) => {
        // TODO: Save assistant message to Supabase
        console.log('Chat completed, saving message:', text.slice(0, 100) + '...');
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');

    // Handle different types of GET requests from AI SDK
    const url = new URL(request.url);
    const pathname = url.pathname;

    // Check if this is a stream data request
    if (pathname.includes('streamData')) {
      return new Response('', { status: 200 });
    }

    // Check if this is an error request
    if (pathname.includes('error')) {
      return new Response('', { status: 200 });
    }

    if (!chatId) {
      return new ChatSDKError('bad_request:api').toResponse();
    }

    const userId = request.headers.get("x-user-id");

    if (!userId) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    // TODO: Implement resume functionality - return chat messages
    return Response.json({ messages: [] });
  } catch (error) {
    console.error('Chat GET API error:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }
}
