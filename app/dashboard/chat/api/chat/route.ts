import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { ChatSDKError } from '../../lib/errors';

export async function POST(request: NextRequest) {
  try {
    const { id, message, selectedChatModel, selectedVisibilityType } = await request.json();

    console.log('API /dashboard/chat/api/chat: Received request for chat ID:', id);
    console.log('API: Message content:', message?.content || 'No content');

    // Get user ID from header (for Supabase auth)
    const userId = request.headers.get("x-user-id");

    if (!userId) {
      console.log('API: No user ID in headers');
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    // TODO: Validate chat access and load chat history from Supabase
    // TODO: Save user message to Supabase

    const result = await streamText({
      model: openai(selectedChatModel || 'gpt-4o'),
      messages: [message], // In a real implementation, load full conversation history
      onFinish: async ({ text }) => {
        // TODO: Save assistant message to Supabase
        console.log('Chat completed, saving message:', text.slice(0, 100) + '...');
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }
}

export async function GET(request: NextRequest) {
  // Handle resume functionality
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  const userId = request.headers.get("x-user-id");
  
  if (!userId) {
    return new ChatSDKError('unauthorized:chat').toResponse();
  }

  // TODO: Implement resume functionality
  return new Response(null, { status: 204 });
}
