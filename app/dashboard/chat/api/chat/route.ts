import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { NextRequest } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { ChatSDKError } from '../../lib/errors';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Dashboard Chat API: Received request body:', body);

    const { messages, id, selectedChatModel, selectedVisibilityType } = body;

    // Get user from Supabase auth
    const supabase = createServerComponentClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      console.log('Dashboard Chat API: No authenticated user');
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    console.log('Dashboard Chat API: Processing messages for user:', session.user.id);

    // Create a system prompt that encourages code generation
    const systemPrompt = `You are Provibe AI, a helpful assistant that specializes in providing practical code examples and solutions.

IMPORTANT INSTRUCTIONS:
- When users ask for code, ALWAYS provide complete, working code examples
- Use proper markdown code blocks with language specification (e.g., \`\`\`python, \`\`\`javascript, \`\`\`html)
- Provide runnable, practical examples rather than just explanations
- Include comments in your code to explain key concepts
- For web development, provide complete HTML documents when appropriate
- Always be helpful and provide actual code rather than just describing what the code should do

Examples of good responses:
- User asks for Python Fibonacci: Provide the actual function in a \`\`\`python code block
- User asks for JavaScript: Provide working JS code in a \`\`\`javascript code block
- User asks for HTML: Provide complete HTML documents

Your code will be automatically detected and displayed in an interactive artifact panel for easy viewing and copying.`;

    // Prepare messages with system prompt
    const conversationMessages = [
      { role: 'system' as const, content: systemPrompt },
      ...messages
    ];

    const latestMessage = messages[messages.length - 1];
    const messageContent = latestMessage?.content || '';
    console.log('Dashboard Chat API: Generating response for:', messageContent.slice(0, 100) + '...');

    const result = streamText({
      model: openai(selectedChatModel || 'gpt-4o'),
      messages: conversationMessages,
      onFinish: async ({ text }) => {
        console.log('Dashboard Chat API: Response generated, length:', text.length);
        // TODO: Save to Supabase if needed
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Dashboard Chat API error:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }
}

export async function GET(request: NextRequest) {
  try {
    // Handle AI SDK status/error requests
    const url = new URL(request.url);
    const pathname = url.pathname;

    if (pathname.includes('error')) {
      return new Response('', { status: 200 });
    }

    if (pathname.includes('status')) {
      return Response.json({ status: 'ready' });
    }

    if (pathname.includes('streamData')) {
      return new Response('', { status: 200 });
    }

    // Default response for other GET requests
    return new Response('', { status: 200 });
  } catch (error) {
    console.error('Dashboard Chat API GET error:', error);
    return new Response('', { status: 200 });
  }
}
