import { NextRequest } from 'next/server';
import { ChatSDKError } from '../../lib/errors';

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get("x-user-id");
    
    if (!userId) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    // TODO: Fetch chat history from Supabase
    // For now, return empty history
    const history = {
      chats: []
    };

    return Response.json(history);
  } catch (error) {
    console.error('History API error:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }
}
