import { NextRequest } from 'next/server';
import { ChatSDKError } from '../../lib/errors';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');

    if (!chatId) {
      return new ChatSDKError('bad_request:api').toResponse();
    }

    const userId = request.headers.get("x-user-id");
    
    if (!userId) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    // TODO: Fetch votes from Supabase
    // For now, return empty array
    return Response.json([]);
  } catch (error) {
    console.error('Vote API error:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }
}
