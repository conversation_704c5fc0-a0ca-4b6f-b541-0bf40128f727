'use client';

import type { Attachment, UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { useArtifact } from '../hooks/use-artifact';
import type { VisibilityType } from './visibility-selector';
import { Button } from '@/components/ui/button';
import { XIcon } from './icons';
import { Markdown } from './markdown';
import MultimodalInput from './multimodal-input';


export type ArtifactKind = 'text' | 'code' | 'html' | 'markdown' | 'csv';

export interface UIArtifact {
  title: string;
  documentId: string;
  kind: ArtifactKind;
  content: string;
  isVisible: boolean;
  status: 'streaming' | 'idle';
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
}

interface ArtifactProps {
  chatId: string;
  input: string;
  setInput: (value: string) => void;
  handleSubmit: UseChatHelpers['handleSubmit'];
  status: string;
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: (attachments: Array<Attachment>) => void;
  append: UseChatHelpers['append'];
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  votes?: Array<any>;
  isReadonly: boolean;
  selectedVisibilityType: VisibilityType;
}

export function Artifact({
  chatId,
  input,
  setInput,
  handleSubmit,
  status,
  stop,
  attachments,
  setAttachments,
  append,
  messages,
  setMessages,
  reload,
  votes,
  isReadonly,
  selectedVisibilityType,
}: ArtifactProps) {
  const { artifact, setArtifact } = useArtifact();

  if (!artifact.isVisible) {
    return null;
  }

  const handleClose = () => {
    setArtifact((prev) => ({ ...prev, isVisible: false }));
  };

  return (
    <div className="fixed inset-y-0 right-0 w-1/2 bg-background border-l shadow-lg z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold">{artifact.title || 'Artifact'}</h2>
          <p className="text-sm text-muted-foreground">
            {artifact.kind.charAt(0).toUpperCase() + artifact.kind.slice(1)} artifact
          </p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <XIcon className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden flex flex-col">
        <div className="flex-1 p-4 overflow-auto">
          {artifact.kind === 'code' && (
            <div className="h-full">
              <pre className="bg-muted p-4 rounded-lg overflow-auto h-full">
                <code className="text-sm font-mono">{artifact.content}</code>
              </pre>
            </div>
          )}

          {artifact.kind === 'text' && (
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <div className="whitespace-pre-wrap">{artifact.content}</div>
            </div>
          )}

          {artifact.kind === 'html' && (
            <div className="w-full h-full">
              <iframe
                srcDoc={artifact.content}
                className="w-full h-full border rounded-lg"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          )}

          {artifact.kind === 'markdown' && (
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <Markdown content={artifact.content} />
            </div>
          )}

          {artifact.kind === 'csv' && (
            <div className="overflow-auto h-full">
              <table className="w-full border-collapse border border-gray-300">
                {artifact.content.split('\n').map((row, i) => (
                  <tr key={i}>
                    {row.split(',').map((cell, j) => (
                      <td key={j} className="border border-gray-300 px-2 py-1 text-sm">
                        {cell.trim()}
                      </td>
                    ))}
                  </tr>
                ))}
              </table>
            </div>
          )}

          {artifact.status === 'streaming' && (
            <div className="flex items-center gap-2 mt-4 p-2 bg-muted rounded">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
              <span className="text-sm">Updating artifact...</span>
            </div>
          )}
        </div>

        {/* Chat input for artifact-specific conversation */}
        <div className="border-t p-4">
          <MultimodalInput
            chatId={chatId}
            input={input}
            setInput={setInput}
            handleSubmit={handleSubmit}
            status={status}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
            append={append}
            selectedVisibilityType={selectedVisibilityType}
          />
        </div>
      </div>
    </div>
  );
}
