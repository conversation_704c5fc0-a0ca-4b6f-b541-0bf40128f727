'use client';

import type { Attachment, UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { useArtifact } from '../hooks/use-artifact';
import type { VisibilityType } from './visibility-selector';
import { Button } from '@/components/ui/button';
import { XIcon } from './icons';

export type ArtifactKind = 'text' | 'code' | 'html' | 'markdown' | 'csv';

export interface UIArtifact {
  title: string;
  documentId: string;
  kind: ArtifactKind;
  content: string;
  isVisible: boolean;
  status: 'streaming' | 'idle';
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
}

interface ArtifactProps {
  chatId: string;
  input: string;
  setInput: (value: string) => void;
  handleSubmit: UseChatHelpers['handleSubmit'];
  status: string;
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: (attachments: Array<Attachment>) => void;
  append: UseChatHelpers['append'];
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  votes?: Array<any>;
  isReadonly: boolean;
  selectedVisibilityType: VisibilityType;
}

export function Artifact({
  chatId,
  input,
  setInput,
  handleSubmit,
  status,
  stop,
  attachments,
  setAttachments,
  append,
  messages,
  setMessages,
  reload,
  votes,
  isReadonly,
  selectedVisibilityType,
}: ArtifactProps) {
  const { artifact, setArtifact } = useArtifact();

  if (!artifact.isVisible) {
    return null;
  }

  const handleClose = () => {
    setArtifact((prev) => ({ ...prev, isVisible: false }));
  };

  return (
    <div className="fixed inset-y-0 right-0 w-1/2 bg-background border-l shadow-lg z-50">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">{artifact.title || 'Artifact'}</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0"
        >
          <XIcon className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="flex-1 p-4 overflow-auto">
        {artifact.kind === 'code' && (
          <pre className="bg-muted p-4 rounded-lg overflow-auto">
            <code>{artifact.content}</code>
          </pre>
        )}
        
        {artifact.kind === 'text' && (
          <div className="prose prose-sm max-w-none">
            {artifact.content}
          </div>
        )}
        
        {artifact.kind === 'html' && (
          <div 
            className="w-full h-full border rounded-lg"
            dangerouslySetInnerHTML={{ __html: artifact.content }}
          />
        )}
        
        {artifact.kind === 'markdown' && (
          <div className="prose prose-sm max-w-none">
            {/* TODO: Add markdown renderer */}
            <pre>{artifact.content}</pre>
          </div>
        )}
        
        {artifact.kind === 'csv' && (
          <div className="overflow-auto">
            {/* TODO: Add CSV table renderer */}
            <pre className="text-sm">{artifact.content}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
