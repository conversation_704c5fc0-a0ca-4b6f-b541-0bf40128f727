'use client';

import { But<PERSON> } from '@/components/ui/button';
import { PlusIcon } from './icons';
import { VisibilitySelector, type VisibilityType } from './visibility-selector';
import { useRouter } from 'next/navigation';

interface ChatHeaderProps {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
}

export function ChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
}: ChatHeaderProps) {
  const router = useRouter();

  return (
    <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 border-b">
      <Button
        variant="outline"
        className="md:px-2 px-2 md:h-fit"
        onClick={() => {
          router.push('/dashboard/chat');
          router.refresh();
        }}
      >
        <PlusIcon className="h-4 w-4" />
        <span className="md:sr-only">New Chat</span>
      </Button>

      {!isReadonly && (
        <VisibilitySelector
          chatId={chatId}
          selectedVisibilityType={selectedVisibilityType}
          className="ml-auto"
        />
      )}
    </header>
  );
}
