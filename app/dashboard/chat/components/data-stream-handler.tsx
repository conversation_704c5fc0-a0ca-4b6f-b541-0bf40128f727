'use client';

import { useEffect } from 'react';
import type { UIMessage } from 'ai';
import { useArtifact } from '../hooks/use-artifact';

interface DataStreamHandlerProps {
  messages: Array<UIMessage>;
}

export function DataStreamHandler({ messages }: DataStreamHandlerProps) {
  const { setArtifact } = useArtifact();

  useEffect(() => {
    // Look for artifact data in the latest assistant message
    const latestMessage = messages.findLast(m => m.role === 'assistant');

    if (latestMessage?.content) {
      const content = latestMessage.content;

      // Enhanced code detection - look for various code patterns
      const codeBlockMatch = content.match(/```(\w+)?\n([\s\S]*?)```/);
      if (codeBlockMatch) {
        const language = codeBlockMatch[1] || 'text';
        const code = codeBlockMatch[2].trim();

        if (code.length > 10) { // Only create artifact for substantial code
          setArtifact({
            documentId: `artifact-${Date.now()}`,
            title: `${language.charAt(0).toUpperCase() + language.slice(1)} Code`,
            kind: 'code',
            content: code,
            isVisible: true,
            status: 'idle',
            boundingBox: { top: 0, left: 0, width: 0, height: 0 },
          });
          return;
        }
      }

      // Detect inline code patterns for common languages
      const pythonMatch = content.match(/def\s+\w+\(.*?\):|for\s+\w+\s+in\s+|if\s+__name__\s*==\s*['"]\s*__main__\s*['"]:/);
      const jsMatch = content.match(/function\s+\w+\(.*?\)|const\s+\w+\s*=|let\s+\w+\s*=|console\.log\(/);
      const htmlMatch = content.match(/<html|<!DOCTYPE html|<div|<span|<p>/i);

      if (pythonMatch) {
        // Extract Python code from the message
        const lines = content.split('\n');
        const codeLines = lines.filter(line =>
          line.trim().startsWith('def ') ||
          line.trim().startsWith('for ') ||
          line.trim().startsWith('if ') ||
          line.trim().startsWith('return ') ||
          line.trim().startsWith('print(') ||
          line.includes('=') && !line.includes('?') ||
          line.trim() === '' // Include empty lines for formatting
        );

        if (codeLines.length > 2) {
          setArtifact({
            documentId: `artifact-${Date.now()}`,
            title: 'Python Code',
            kind: 'code',
            content: codeLines.join('\n'),
            isVisible: true,
            status: 'idle',
            boundingBox: { top: 0, left: 0, width: 0, height: 0 },
          });
          return;
        }
      }

      if (jsMatch) {
        // Extract JavaScript code
        const lines = content.split('\n');
        const codeLines = lines.filter(line =>
          line.includes('function') ||
          line.includes('const ') ||
          line.includes('let ') ||
          line.includes('console.log') ||
          line.includes('=>') ||
          line.includes('return ') ||
          line.trim() === ''
        );

        if (codeLines.length > 1) {
          setArtifact({
            documentId: `artifact-${Date.now()}`,
            title: 'JavaScript Code',
            kind: 'code',
            content: codeLines.join('\n'),
            isVisible: true,
            status: 'idle',
            boundingBox: { top: 0, left: 0, width: 0, height: 0 },
          });
          return;
        }
      }

      // Detect HTML content
      if (htmlMatch && content.length > 50) {
        setArtifact({
          documentId: `artifact-${Date.now()}`,
          title: 'HTML Document',
          kind: 'html',
          content: content,
          isVisible: true,
          status: 'idle',
          boundingBox: { top: 0, left: 0, width: 0, height: 0 },
        });
        return;
      }

      // Detect CSV data
      if (content.includes(',') && content.split('\n').length > 3) {
        const lines = content.split('\n').filter(line => line.trim());
        const csvLines = lines.filter(line => line.includes(','));

        if (csvLines.length >= 3 && csvLines.length / lines.length > 0.7) {
          setArtifact({
            documentId: `artifact-${Date.now()}`,
            title: 'CSV Data',
            kind: 'csv',
            content: csvLines.join('\n'),
            isVisible: true,
            status: 'idle',
            boundingBox: { top: 0, left: 0, width: 0, height: 0 },
          });
          return;
        }
      }
    }
  }, [messages, setArtifact]);

  return null;
}
