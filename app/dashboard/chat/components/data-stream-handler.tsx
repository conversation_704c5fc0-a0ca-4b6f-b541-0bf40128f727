'use client';

import { useEffect } from 'react';
import type { UIMessage } from 'ai';
import { useArtifact } from '../hooks/use-artifact';

interface DataStreamHandlerProps {
  messages: Array<UIMessage>;
}

export function DataStreamHandler({ messages }: DataStreamHandlerProps) {
  const { setArtifact } = useArtifact();

  useEffect(() => {
    // Look for artifact data in the latest assistant message
    const latestMessage = messages.findLast(m => m.role === 'assistant');
    
    if (latestMessage?.content) {
      // Simple artifact detection - look for code blocks or specific patterns
      const content = latestMessage.content;
      
      // Detect code artifacts
      const codeMatch = content.match(/```(\w+)?\n([\s\S]*?)```/);
      if (codeMatch) {
        const language = codeMatch[1] || 'text';
        const code = codeMatch[2];
        
        setArtifact({
          documentId: `artifact-${Date.now()}`,
          title: `${language.charAt(0).toUpperCase() + language.slice(1)} Code`,
          kind: 'code',
          content: code,
          isVisible: true,
          status: 'idle',
          boundingBox: {
            top: 0,
            left: 0,
            width: 0,
            height: 0,
          },
        });
        return;
      }
      
      // Detect HTML artifacts
      if (content.includes('<html') || content.includes('<!DOCTYPE html')) {
        setArtifact({
          documentId: `artifact-${Date.now()}`,
          title: 'HTML Document',
          kind: 'html',
          content: content,
          isVisible: true,
          status: 'idle',
          boundingBox: {
            top: 0,
            left: 0,
            width: 0,
            height: 0,
          },
        });
        return;
      }
      
      // Detect CSV data
      if (content.includes(',') && content.split('\n').length > 2) {
        const lines = content.split('\n');
        const hasCommas = lines.every(line => line.includes(','));
        
        if (hasCommas) {
          setArtifact({
            documentId: `artifact-${Date.now()}`,
            title: 'CSV Data',
            kind: 'csv',
            content: content,
            isVisible: true,
            status: 'idle',
            boundingBox: {
              top: 0,
              left: 0,
              width: 0,
              height: 0,
            },
          });
          return;
        }
      }
    }
  }, [messages, setArtifact]);

  return null; // This component doesn't render anything
}
