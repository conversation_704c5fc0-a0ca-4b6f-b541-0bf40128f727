import { 
  Check, 
  ChevronDown, 
  Globe, 
  Lock,
  Plus,
  MoreHorizontal,
  ArrowUp,
  Square,
  Paperclip,
  X
} from 'lucide-react';

export const CheckCircleFillIcon = Check;
export const ChevronDownIcon = ChevronDown;
export const GlobeIcon = Globe;
export const LockIcon = Lock;
export const PlusIcon = Plus;
export const MoreHorizontalIcon = MoreHorizontal;
export const ArrowUpIcon = ArrowUp;
export const StopIcon = Square;
export const PaperclipIcon = Paperclip;
export const XIcon = X;

// Vercel icon placeholder
export const VercelIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M12 2L2 19.777h20L12 2z" />
  </svg>
);
