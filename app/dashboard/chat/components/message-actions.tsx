'use client';

import type { UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { Button } from '@/components/ui/button';
import { ThumbsUpIcon, ThumbsDownIcon, CopyIcon, ReloadIcon } from './icons';
import { useState } from 'react';

interface MessageActionsProps {
  chatId: string;
  message: UIMessage;
  vote?: any;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
}

export function MessageActions({
  chatId,
  message,
  vote,
  setMessages,
  reload,
}: MessageActionsProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    await navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleVote = async (type: 'up' | 'down') => {
    // TODO: Implement voting functionality
    console.log('Vote:', type, 'for message:', message.id);
  };

  const handleReload = () => {
    reload();
  };

  return (
    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
      <Button
        variant="ghost"
        size="sm"
        onClick={handleCopy}
        className="h-6 w-6 p-0"
        title="Copy message"
      >
        <CopyIcon className="h-3 w-3" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVote('up')}
        className="h-6 w-6 p-0"
        title="Good response"
      >
        <ThumbsUpIcon className="h-3 w-3" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVote('down')}
        className="h-6 w-6 p-0"
        title="Bad response"
      >
        <ThumbsDownIcon className="h-3 w-3" />
      </Button>
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleReload}
        className="h-6 w-6 p-0"
        title="Regenerate response"
      >
        <ReloadIcon className="h-3 w-3" />
      </Button>
    </div>
  );
}
