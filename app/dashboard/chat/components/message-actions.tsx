'use client';

import type { UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { Button } from '@/components/ui/button';
import { ThumbsUpIcon, ThumbsDownIcon, CopyIcon, ReloadIcon, PlusIcon } from './icons';
import { useState } from 'react';
import { useArtifact } from '../hooks/use-artifact';

interface MessageActionsProps {
  chatId: string;
  message: UIMessage;
  vote?: any;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
}

export function MessageActions({
  chatId,
  message,
  vote,
  setMessages,
  reload,
}: MessageActionsProps) {
  const [copied, setCopied] = useState(false);
  const { setArtifact } = useArtifact();

  const handleCopy = async () => {
    await navigator.clipboard.writeText(message.content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleVote = async (type: 'up' | 'down') => {
    // TODO: Implement voting functionality
    console.log('Vote:', type, 'for message:', message.id);
  };

  const handleReload = () => {
    reload();
  };

  const handleCreateArtifact = () => {
    const content = message.content;

    // Try to detect what kind of artifact this should be
    if (content.includes('```')) {
      // Extract code from markdown blocks
      const codeMatch = content.match(/```(\w+)?\n([\s\S]*?)```/);
      if (codeMatch) {
        const language = codeMatch[1] || 'text';
        const code = codeMatch[2].trim();

        setArtifact({
          documentId: `artifact-${Date.now()}`,
          title: `${language.charAt(0).toUpperCase() + language.slice(1)} Code`,
          kind: 'code',
          content: code,
          isVisible: true,
          status: 'idle',
          boundingBox: { top: 0, left: 0, width: 0, height: 0 },
        });
        return;
      }
    }

    // Check for code patterns
    const hasCode = content.includes('function') ||
                   content.includes('def ') ||
                   content.includes('console.log') ||
                   content.includes('print(') ||
                   content.includes('import ') ||
                   content.includes('const ') ||
                   content.includes('let ');

    if (hasCode) {
      setArtifact({
        documentId: `artifact-${Date.now()}`,
        title: 'Code Snippet',
        kind: 'code',
        content: content,
        isVisible: true,
        status: 'idle',
        boundingBox: { top: 0, left: 0, width: 0, height: 0 },
      });
    } else {
      // Create as text artifact
      setArtifact({
        documentId: `artifact-${Date.now()}`,
        title: 'Text Document',
        kind: 'text',
        content: content,
        isVisible: true,
        status: 'idle',
        boundingBox: { top: 0, left: 0, width: 0, height: 0 },
      });
    }
  };

  return (
    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
      <Button
        variant="ghost"
        size="sm"
        onClick={handleCopy}
        className="h-6 w-6 p-0"
        title="Copy message"
      >
        <CopyIcon className="h-3 w-3" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={handleCreateArtifact}
        className="h-6 w-6 p-0"
        title="Create artifact from this message"
      >
        <PlusIcon className="h-3 w-3" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVote('up')}
        className="h-6 w-6 p-0"
        title="Good response"
      >
        <ThumbsUpIcon className="h-3 w-3" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleVote('down')}
        className="h-6 w-6 p-0"
        title="Bad response"
      >
        <ThumbsDownIcon className="h-3 w-3" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={handleReload}
        className="h-6 w-6 p-0"
        title="Regenerate response"
      >
        <ReloadIcon className="h-3 w-3" />
      </Button>
    </div>
  );
}
