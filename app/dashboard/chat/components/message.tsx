'use client';

import type { UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { memo } from 'react';
import { cn } from '../lib/utils';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';

interface MessageProps {
  chatId: string;
  message: UIMessage;
  isLoading?: boolean;
  vote?: any;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
}

function PureMessage({
  chatId,
  message,
  isLoading,
  vote,
  setMessages,
  reload,
  isReadonly,
}: MessageProps) {
  return (
    <div
      className={cn(
        'flex flex-col gap-2 px-4 py-2 group',
        message.role === 'user' ? 'items-end' : 'items-start'
      )}
    >
      <div className="flex items-center gap-2">
        <div className="font-semibold text-sm">
          {message.role === 'user' ? 'You' : 'Assistant'}
        </div>
        {message.createdAt && (
          <div className="text-xs text-muted-foreground">
            {new Date(message.createdAt).toLocaleTimeString()}
          </div>
        )}
      </div>
      
      <div
        className={cn(
          'max-w-[80%] rounded-lg px-4 py-2',
          message.role === 'user'
            ? 'bg-primary text-primary-foreground ml-auto'
            : 'bg-muted'
        )}
      >
        {message.role === 'user' ? (
          <div className="whitespace-pre-wrap">{message.content}</div>
        ) : (
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <Markdown content={message.content} />
            {isLoading && (
              <div className="flex items-center gap-2 mt-2">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current"></div>
                <span className="text-xs">Generating...</span>
              </div>
            )}
          </div>
        )}
      </div>

      {message.role === 'assistant' && !isReadonly && (
        <MessageActions
          chatId={chatId}
          message={message}
          vote={vote}
          setMessages={setMessages}
          reload={reload}
        />
      )}
    </div>
  );
}

export const Message = memo(PureMessage, (prevProps, nextProps) => {
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.message.content !== nextProps.message.content) return false;
  if (prevProps.vote !== nextProps.vote) return false;
  return true;
});
