'use client';

import type { UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';

interface MessagesProps {
  chatId: string;
  status: string;
  votes?: Array<any>;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  isArtifactVisible: boolean;
}

export function Messages({
  chatId,
  status,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
  isArtifactVisible,
}: MessagesProps) {
  return (
    <div className="flex flex-col flex-1 overflow-y-auto px-4">
      <div className="flex flex-col gap-4 py-4">
        {messages.map((message) => (
          <div key={message.id} className="flex flex-col gap-2">
            <div className="font-semibold">
              {message.role === 'user' ? 'You' : 'Assistant'}
            </div>
            <div className="prose prose-sm max-w-none">
              {message.content}
            </div>
          </div>
        ))}
        {status === 'loading' && (
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
            <span>Thinking...</span>
          </div>
        )}
      </div>
    </div>
  );
}
