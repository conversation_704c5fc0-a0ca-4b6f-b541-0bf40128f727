'use client';

import type { Attachment, UIMessage } from 'ai';
import type { UseChatHelpers } from '@ai-sdk/react';
import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ArrowUpIcon, StopIcon, PaperclipIcon, XIcon } from './icons';
import type { VisibilityType } from './visibility-selector';

interface MultimodalInputProps {
  chatId: string;
  input: string;
  setInput: (value: string) => void;
  handleSubmit: UseChatHelpers['handleSubmit'];
  status: string;
  stop: () => void;
  attachments: Array<Attachment>;
  setAttachments: (attachments: Array<Attachment>) => void;
  messages: Array<UIMessage>;
  setMessages: UseChatHelpers['setMessages'];
  append: UseChatHelpers['append'];
  selectedVisibilityType: VisibilityType;
}

export function MultimodalInput({
  chatId,
  input,
  setInput,
  handleSubmit,
  status,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  append,
  selectedVisibilityType,
}: MultimodalInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (status === 'ready' && input.trim()) {
        handleSubmit(e as any);
      }
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    // Convert files to attachments
    const newAttachments: Array<Attachment> = [];

    for (const file of files) {
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        newAttachments.push({
          name: file.name,
          contentType: file.type,
          url,
        });
      } else {
        // For non-image files, read as text
        const text = await file.text();
        newAttachments.push({
          name: file.name,
          contentType: file.type,
          url: `data:${file.type};base64,${btoa(text)}`,
        });
      }
    }

    setAttachments([...attachments, ...newAttachments]);

    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  return (
    <div className="relative flex flex-col gap-2 w-full">
      {/* Attachments preview */}
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 bg-muted rounded-lg">
          {attachments.map((attachment, index) => (
            <div
              key={index}
              className="flex items-center gap-2 bg-background px-2 py-1 rounded text-sm"
            >
              <span className="truncate max-w-[200px]">{attachment.name}</span>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={() => removeAttachment(index)}
                className="h-4 w-4 p-0"
              >
                <XIcon className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      <div className="relative flex items-end gap-2 w-full">
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Send a message..."
            className="min-h-[60px] max-h-[200px] resize-none pr-12"
            rows={3}
          />

          <div className="absolute right-2 bottom-2 flex items-center gap-1">
            <Button
              type="button"
              size="sm"
              variant="ghost"
              onClick={() => fileInputRef.current?.click()}
              className="h-8 w-8 p-0"
              title="Attach files"
            >
              <PaperclipIcon className="h-4 w-4" />
            </Button>

            {status === 'loading' ? (
              <Button
                type="button"
                size="sm"
                onClick={stop}
                className="h-8 w-8 p-0"
                title="Stop generation"
              >
                <StopIcon className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                type="submit"
                size="sm"
                disabled={!input.trim() || status !== 'ready'}
                onClick={(e) => handleSubmit(e as any)}
                className="h-8 w-8 p-0"
                title="Send message"
              >
                <ArrowUpIcon className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,.pdf,.txt,.md,.json,.csv"
        />
      </div>
    </div>
  );
}
