'use client';

import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

const suggestions = [
  {
    title: "Create a new project",
    description: "Start a new project with AI assistance",
    action: "Help me create a new project from scratch"
  },
  {
    title: "Write a PRD",
    description: "Generate a Product Requirements Document",
    action: "Help me write a comprehensive PRD for my product idea"
  },
  {
    title: "Brainstorm features",
    description: "Generate feature ideas for your product",
    action: "Help me brainstorm innovative features for my product"
  },
  {
    title: "Technical planning",
    description: "Plan your technical architecture",
    action: "Help me plan the technical architecture for my project"
  },
  {
    title: "Market research",
    description: "Research your target market",
    action: "Help me research my target market and competitors"
  },
  {
    title: "User stories",
    description: "Create user stories and requirements",
    action: "Help me write user stories for my application"
  }
];

export function SuggestedActions() {
  const handleSuggestionClick = (action: string) => {
    // This will be handled by the parent component
    // For now, we'll dispatch a custom event
    window.dispatchEvent(new CustomEvent('suggestion-click', { 
      detail: { action } 
    }));
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl w-full">
      {suggestions.map((suggestion, index) => (
        <motion.div
          key={suggestion.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <Button
            variant="outline"
            className="h-auto p-4 text-left flex flex-col items-start gap-2 hover:bg-muted/50 transition-colors"
            onClick={() => handleSuggestionClick(suggestion.action)}
          >
            <div className="font-semibold">{suggestion.title}</div>
            <div className="text-sm text-muted-foreground">
              {suggestion.description}
            </div>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}
