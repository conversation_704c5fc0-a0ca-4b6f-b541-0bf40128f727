import { toast as sonnerToast } from 'sonner';

export const toast = ({
  type,
  description,
}: {
  type: 'success' | 'error' | 'info';
  description: string;
}) => {
  switch (type) {
    case 'success':
      return sonnerToast.success(description);
    case 'error':
      return sonnerToast.error(description);
    case 'info':
      return sonnerToast.info(description);
    default:
      return sonnerToast(description);
  }
};
