// /app/dashboard/chat/layout.tsx
'use client'

import React from 'react';
import { SWRConfig } from 'swr';

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SWRConfig
      value={{
        fetcher: (url: string) => fetch(url).then((res) => res.json()),
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
      }}
    >
      {/* This div establishes the full viewport height and flex column layout */}
      <div className="flex flex-col h-screen w-full">{children}</div>
    </SWRConfig>
  );
}
