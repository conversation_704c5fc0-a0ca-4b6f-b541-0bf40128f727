import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';

config({
  path: '../../../.env.local',
});

const runMigrate = async () => {
  const databaseUrl = process.env.SUPABASE_DB_URL || process.env.POSTGRES_URL;
  
  if (!databaseUrl) {
    throw new Error('SUPABASE_DB_URL or POSTGRES_URL is not defined');
  }

  const connection = postgres(databaseUrl, { max: 1 });
  const db = drizzle(connection);

  console.log('⏳ Running migrations...');

  const start = Date.now();
  await migrate(db, { migrationsFolder: './lib/db/migrations' });
  const end = Date.now();

  console.log('✅ Migrations completed in', end - start, 'ms');
  process.exit(0);
};

runMigrate().catch((err) => {
  console.error('❌ Migration failed');
  console.error(err);
  process.exit(1);
});
