"use client"

import { useState, useEffect } from "react"
import { generateUUID } from "./lib/utils"
import { useAuth } from "@/components/auth-provider"
import { useRouter } from "next/navigation"

export default function ChatPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isCreating, setIsCreating] = useState(true);

  // Create a new chat session
  useEffect(() => {
    const createSession = async () => {
      if (!user?.id) {
        router.push('/auth/login');
        return;
      }

      try {
        const response = await fetch('/api/chat/create-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': user.id,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to create session');
        }

        const { sessionId } = await response.json();
        router.push(`/dashboard/chat/${sessionId}`);
      } catch (error) {
        console.error('Error creating chat session:', error);
        // Fallback to generated UUID
        const fallbackId = generateUUID();
        router.push(`/dashboard/chat/${fallbackId}`);
      } finally {
        setIsCreating(false);
      }
    };

    createSession();
  }, [user, router]);

  if (!user) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Authentication required</h2>
          <p className="text-muted-foreground">Please sign in to start chatting.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-4">
          {isCreating ? 'Creating new chat...' : 'Starting chat...'}
        </h2>
        <p className="text-muted-foreground">
          {isCreating ? 'Setting up your chat session...' : 'Redirecting to chat...'}
        </p>
      </div>
    </div>
  );
}
