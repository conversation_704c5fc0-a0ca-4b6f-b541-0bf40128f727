"use client"

import { useState, useEffect } from "react"
import { generateUUID } from "./lib/utils"
import { useAuth } from "@/components/auth-provider"
import { useRouter } from "next/navigation"

export default function ChatPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [chatId, setChatId] = useState<string>(() => generateUUID());

  // Redirect to specific chat session
  useEffect(() => {
    router.push(`/dashboard/chat/${chatId}`);
  }, [chatId, router]);

  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-4">Starting new chat...</h2>
        <p className="text-muted-foreground">Redirecting to chat session...</p>
      </div>
    </div>
  );
}
