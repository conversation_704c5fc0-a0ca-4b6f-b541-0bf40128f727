// Add agent selector dropdown
export function ChatHeader() {
  const { 
    selectedModel, 
    setSelectedModel, 
    selectedProject, 
    setSelectedProject,
    selectedDocType,
    setSelectedDocType,
    projects,
    documentTypes,
    selectedAgent,
    setSelectedAgent,
    agents
  } = useChat();
  
  return (
    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between border-b pb-4">
      <h2 className="text-lg font-semibold">Chat</h2>
      
      <div className="flex flex-wrap gap-2">
        {/* Agent selector */}
        <Select value={selectedAgent} onValueChange={setSelectedAgent}>
          <SelectTrigger className="h-8 w-[180px]">
            <SelectValue placeholder="Select agent" />
          </SelectTrigger>
          <SelectContent>
            {agents.map((agent) => (
              <SelectItem key={agent.id} value={agent.id}>
                <div className="flex items-center">
                  <span className="mr-2">{agent.icon}</span>
                  {agent.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {/* Existing model selector */}
        <Select value={selectedModel} onValueChange={setSelectedModel}>
          {/* existing content */}
        </Select>
        
        {/* Existing project selector */}
        <Select value={selectedProject} onValueChange={setSelectedProject}>
          {/* existing content */}
        </Select>
        
        {/* Existing document type selector */}
        <Select value={selectedDocType} onValueChange={setSelectedDocType}>
          {/* existing content */}
        </Select>
      </div>
    </div>
  );
}