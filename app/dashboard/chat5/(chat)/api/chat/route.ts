import {
  appendClientMessage,
  appendResponseMessages,
  createDataStream,
  smoothStream,
  streamText,
} from 'ai';
import { createClient } from '@supabase/supabase-js';
import { type RequestHints, systemPrompt } from '../../../lib/ai/prompts';
import {
  createStreamId,
  deleteChatById,
  getChatById,
  getMessageCountByUserId,
  getMessagesByChatId,
  getStreamIdsByChatId,
  saveChat,
  saveMessages,
} from '../../../lib/db/queries';
import { generateUUID, getTrailingMessageId } from '../../../lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { createDocument } from '../../../lib/ai/tools/create-document';
import { updateDocument } from '../../../lib/ai/tools/update-document';
import { requestSuggestions } from '../../../lib/ai/tools/request-suggestions';
import { getWeather } from '../../../lib/ai/tools/get-weather';
import { isProductionEnvironment } from '../../../lib/constants';
import { myProvider } from '../../../lib/ai/providers';
import { entitlementsByUserType } from '../../../lib/ai/entitlements';
import { postRequestBodySchema, type PostRequestBody } from './schema';
import { geolocation } from '@vercel/functions';
import {
  createResumableStreamContext,
  type ResumableStreamContext,
} from 'resumable-stream';
import { after } from 'next/server';
import type { Chat } from '../../../lib/db/schema';
import { differenceInSeconds } from 'date-fns';
import { ChatSDKError } from '../../../lib/errors';

// Initialize Supabase client for auth
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export const maxDuration = 60;

let globalStreamContext: ResumableStreamContext | null = null;

function getStreamContext() {
  if (!globalStreamContext) {
    try {
      globalStreamContext = createResumableStreamContext({
        waitUntil: after,
      });
    } catch (error: any) {
      if (error.message.includes('REDIS_URL')) {
        console.log(
          ' > Resumable streams are disabled due to missing REDIS_URL',
        );
      } else {
        console.error(error);
      }
    }
  }

  return globalStreamContext;
}

export async function POST(request: Request) {
  let requestBody: PostRequestBody;

  let json: any;
  try {
    json = await request.json();
    console.log('API: Received JSON body:', JSON.stringify(json, null, 2));
  } catch (error) {
    console.error('API: Failed to parse JSON:', error);
    return new ChatSDKError('bad_request:api').toResponse();
  }

  try {
    requestBody = postRequestBodySchema.parse(json);
    console.log('API: Schema validation passed');
  } catch (error) {
    console.error('API: Request body validation failed:', error);
    console.error('API: Schema validation error details:', JSON.stringify(error, null, 2));
    console.error('API: Received JSON that failed validation:', JSON.stringify(json, null, 2));
    return new ChatSDKError('bad_request:api').toResponse();
  }

  try {
    const { id, message, selectedChatModel, selectedVisibilityType } =
      requestBody;

    console.log('API /dashboard/chat5/api/chat: Received request for chat ID:', id);
    console.log('API: Message content:', message.parts?.[0]?.text || 'No text content');

    // Get user ID from header (similar to your existing auth pattern)
    const userId = request.headers.get("x-user-id");

    if (!userId) {
      console.log('API: No user ID in headers');
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    console.log('API: User ID from header:', userId);

    // Convert message to proper format for AI SDK early
    const formattedMessage = {
      id: message.id,
      role: message.role,
      content: message.content,
      createdAt: message.createdAt ? new Date(message.createdAt) : new Date(),
      parts: message.parts?.map((part: any) => ({
        type: 'text' as const,
        text: part.text || part.content || '',
      })) || [{ type: 'text' as const, text: message.content }],
      experimental_attachments: message.experimental_attachments || [],
    };

    // Get user from Supabase to check subscription level
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('subscription_tier')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    const userType = userData.subscription_tier === 'pro' ? 'pro' : 'free';

    const messageCount = await getMessageCountByUserId({
      id: userId,
      differenceInHours: 24,
    });

    if (messageCount > entitlementsByUserType[userType].maxMessagesPerDay) {
      return new ChatSDKError('rate_limit:chat').toResponse();
    }

    console.log('API: Checking if chat exists with ID:', id);
    const chat = await getChatById({ id });

    if (!chat) {
      console.log('API: Chat does not exist, creating new chat');
      try {
        const title = await generateTitleFromUserMessage({
          message: formattedMessage,
        });
        console.log('API: Generated title:', title);

        const savedChat = await saveChat({
          id,
          userId: userId,
          title,
          visibility: selectedVisibilityType as 'public' | 'private',
        });
        console.log('API: Successfully saved chat:', savedChat);
      } catch (error) {
        console.error('API: Error saving chat:', error);
        return new ChatSDKError('bad_request:database', 'Failed to create chat').toResponse();
      }
    } else {
      console.log('API: Chat exists, checking ownership');
      if (chat.userId !== userId) {
        console.log('API: User does not own this chat');
        return new ChatSDKError('forbidden:chat').toResponse();
      }
      console.log('API: User owns this chat, proceeding');
    }

    console.log('API: Getting previous messages for chat:', id);
    const previousMessages = await getMessagesByChatId({ id });
    console.log('API: Found', previousMessages.length, 'previous messages');



    const messages = appendClientMessage({
      // @ts-expect-error: todo add type conversion from DBMessage[] to UIMessage[]
      messages: previousMessages,
      message: formattedMessage,
    });

    const { longitude, latitude, city, country } = geolocation(request);

    const requestHints: RequestHints = {
      longitude,
      latitude,
      city,
      country,
    };

    console.log('API: Saving user message to database');
    try {
      await saveMessages({
        messages: [
          {
            chatId: id,
            id: message.id,
            role: 'user',
            parts: message.parts,
            attachments: message.experimental_attachments ?? [],
            createdAt: new Date().toISOString(),
          },
        ],
      });
      console.log('API: Successfully saved user message');
    } catch (error) {
      console.error('API: Error saving user message:', error);
      return new ChatSDKError('bad_request:database', 'Failed to save message').toResponse();
    }

    const streamId = generateUUID();
    await createStreamId({ streamId, chatId: id });

    const stream = createDataStream({
      execute: (dataStream) => {
        // Create a session-like object for the tools
        const session = {
          user: { id: userId }
        };

        console.log('API: Starting text stream generation');
        const result = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: systemPrompt({ selectedChatModel, requestHints }),
          messages,
          maxSteps: 5,
          experimental_activeTools:
            selectedChatModel === 'chat-model-reasoning'
              ? []
              : [
                  'getWeather',
                  'createDocument',
                  'updateDocument',
                  'requestSuggestions',
                ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          tools: {
            getWeather,
            createDocument: createDocument({ session, dataStream }),
            updateDocument: updateDocument({ session, dataStream }),
            requestSuggestions: requestSuggestions({
              session,
              dataStream,
            }),
          },
          onFinish: async ({ response }) => {
            if (userId) {
              try {
                console.log('API: onFinish - Saving assistant message');
                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (message) => message.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  console.error('API: onFinish - No assistant message found!');
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [formattedMessage],
                  responseMessages: response.messages,
                });

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date().toISOString(),
                    },
                  ],
                });
                console.log('API: onFinish - Successfully saved assistant message');
              } catch (error) {
                console.error('API: onFinish - Failed to save assistant message:', error);
              }
            }
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: (error) => {
        console.error('API: Stream error:', error);
        return 'Oops, an error occurred!';
      },
    });

    const streamContext = getStreamContext();

    if (streamContext) {
      return new Response(
        await streamContext.resumableStream(streamId, () => stream),
      );
    } else {
      return new Response(stream);
    }
  } catch (error) {
    console.error('API: Unexpected error in POST /dashboard/chat5/api/chat:', error);
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    return new ChatSDKError('bad_request:api', 'An unexpected error occurred').toResponse();
  }
}

export async function GET(request: Request) {
  const streamContext = getStreamContext();
  const resumeRequestedAt = new Date();

  if (!streamContext) {
    return new Response(null, { status: 204 });
  }

  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  const userId = request.headers.get("x-user-id");
  
  if (!userId) {
    return new ChatSDKError('unauthorized:chat').toResponse();
  }

  let chat: Chat;

  try {
    chat = await getChatById({ id: chatId });
  } catch {
    return new ChatSDKError('not_found:chat').toResponse();
  }

  if (!chat) {
    return new ChatSDKError('not_found:chat').toResponse();
  }

  if (chat.visibility === 'private' && chat.userId !== userId) {
    return new ChatSDKError('forbidden:chat').toResponse();
  }

  const streamIds = await getStreamIdsByChatId({ chatId });

  if (!streamIds.length) {
    return new ChatSDKError('not_found:stream').toResponse();
  }

  const recentStreamId = streamIds.at(-1);

  if (!recentStreamId) {
    return new ChatSDKError('not_found:stream').toResponse();
  }

  const emptyDataStream = createDataStream({
    execute: () => {},
  });

  const stream = await streamContext.resumableStream(
    recentStreamId,
    () => emptyDataStream,
  );

  /*
   * For when the generation is streaming during SSR
   * but the resumable stream has concluded at this point.
   */
  if (!stream) {
    const messages = await getMessagesByChatId({ id: chatId });
    const mostRecentMessage = messages.at(-1);

    if (!mostRecentMessage) {
      return new Response(emptyDataStream, { status: 200 });
    }

    if (mostRecentMessage.role !== 'assistant') {
      return new Response(emptyDataStream, { status: 200 });
    }

    const messageCreatedAt = new Date(mostRecentMessage.createdAt);

    if (differenceInSeconds(resumeRequestedAt, messageCreatedAt) > 15) {
      return new Response(emptyDataStream, { status: 200 });
    }

    const restoredStream = createDataStream({
      execute: (buffer) => {
        buffer.writeData({
          type: 'append-message',
          message: JSON.stringify(mostRecentMessage),
        });
      },
    });

    return new Response(restoredStream, { status: 200 });
  }

  return new Response(stream, { status: 200 });
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new ChatSDKError('bad_request:api').toResponse();
  }

  const userId = request.headers.get("x-user-id");
  
  if (!userId) {
    return new ChatSDKError('unauthorized:chat').toResponse();
  }

  const chat = await getChatById({ id });

  if (chat.userId !== userId) {
    return new ChatSDKError('forbidden:chat').toResponse();
  }

  const deletedChat = await deleteChatById({ id });

  return Response.json(deletedChat, { status: 200 });
}
