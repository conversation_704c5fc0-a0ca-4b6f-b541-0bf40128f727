import type { NextRequest } from 'next/server';
import { getChatsByUserId } from '../../../lib/db/queries';
import { ChatSDKError } from '../../../lib/errors';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;

    const limit = Number.parseInt(searchParams.get('limit') || '10');
    const startingAfter = searchParams.get('starting_after');
    const endingBefore = searchParams.get('ending_before');

    if (startingAfter && endingBefore) {
      return new ChatSDKError(
        'bad_request:api',
        'Only one of starting_after or ending_before can be provided.',
      ).toResponse();
    }

    // Get user ID from header (set by middleware)
    let userId = request.headers.get("x-user-id");

    // For demo/development: use valid UUID if no header
    if (!userId && process.env.NODE_ENV === 'development') {
      userId = '2fbff9b7-8492-4068-b80d-df4d9a5d096d';
    }

    if (!userId) {
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    const chats = await getChatsByUserId({
      id: userId,
      limit,
      startingAfter,
      endingBefore,
    });

    return Response.json(chats);
  } catch (error) {
    console.error('API: Error in GET /api/history:', error);
    console.error('API: Error details:', error instanceof Error ? error.message : 'Unknown error');
    return new ChatSDKError('bad_request:api', 'Internal server error').toResponse();
  }
}
