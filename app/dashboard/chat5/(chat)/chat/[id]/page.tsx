import { cookies } from 'next/headers';

import { Chat } from '../../../components/chat';
import { getChatById, getMessagesByChatId } from '../../../lib/db/queries';
import { DataStreamHandler } from '../../../components/data-stream-handler';
import { DEFAULT_CHAT_MODEL } from '../../../lib/ai/models';
import type { DBMessage } from '../../../lib/db/schema';
import type { Attachment, UIMessage } from 'ai';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;
  const chat = await getChatById({ id });

  // If chat doesn't exist yet, it will be created when the first message is sent
  // This handles the case where user navigates to /chat/[id] before the chat is created
  const messagesFromDb = await getMessagesByChatId({
    id,
  });

  function convertToUIMessages(messages: Array<DBMessage>): Array<UIMessage> {
    return messages.map((message) => ({
      id: message.id,
      parts: message.parts as UIMessage['parts'],
      role: message.role as UIMessage['role'],
      // Note: content will soon be deprecated in @ai-sdk/react
      content: '',
      createdAt: message.createdAt,
      experimental_attachments:
        (message.attachments as Array<Attachment>) ?? [],
    }));
  }

  const cookieStore = await cookies();
  const chatModelFromCookie = cookieStore.get('chat-model');

  // Use chat data if available, otherwise use defaults for new chat
  const chatId = chat?.id || id;
  const initialVisibility = chat?.visibility || 'private';
  const initialModel = chatModelFromCookie?.value || DEFAULT_CHAT_MODEL;

  return (
    <>
      <Chat
        id={chatId}
        initialMessages={convertToUIMessages(messagesFromDb)}
        initialChatModel={initialModel}
        initialVisibilityType={initialVisibility}
        isReadonly={false}
        autoResume={true}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
