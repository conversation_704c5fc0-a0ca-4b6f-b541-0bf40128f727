-- Chat5 Database Schema for Supabase
-- Run this in your Supabase SQL Editor
-- NOTE: Uses existing auth.users table instead of creating new User table

-- Chat table (renamed to avoid conflict with existing chat_sessions)
CREATE TABLE IF NOT EXISTS "chat5_chats" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"createdAt" timestamp with time zone NOT NULL DEFAULT now(),
	"title" text NOT NULL,
	"userId" uuid NOT NULL,
	"visibility" varchar DEFAULT 'private' NOT NULL
);

-- Message table (renamed to avoid conflict)
CREATE TABLE IF NOT EXISTS "chat5_messages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chatId" uuid NOT NULL,
	"role" varchar NOT NULL,
	"parts" jsonb NOT NULL,
	"attachments" jsonb NOT NULL,
	"createdAt" timestamp with time zone NOT NULL DEFAULT now()
);

-- Vote table (for message voting)
CREATE TABLE IF NOT EXISTS "chat5_votes" (
	"chatId" uuid NOT NULL,
	"messageId" uuid NOT NULL,
	"isUpvoted" boolean NOT NULL,
	CONSTRAINT "chat5_votes_chatId_messageId_pk" PRIMARY KEY("chatId","messageId")
);

-- Document table (for artifacts)
CREATE TABLE IF NOT EXISTS "chat5_documents" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"createdAt" timestamp with time zone NOT NULL DEFAULT now(),
	"title" text NOT NULL,
	"content" text,
	"kind" varchar DEFAULT 'text' NOT NULL,
	"userId" uuid NOT NULL,
	CONSTRAINT "chat5_documents_id_createdAt_pk" PRIMARY KEY("id","createdAt")
);

-- Suggestion table (for document suggestions)
CREATE TABLE IF NOT EXISTS "chat5_suggestions" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"documentId" uuid NOT NULL,
	"documentCreatedAt" timestamp with time zone NOT NULL,
	"originalText" text NOT NULL,
	"suggestedText" text NOT NULL,
	"description" text,
	"isResolved" boolean DEFAULT false NOT NULL,
	"userId" uuid NOT NULL,
	"createdAt" timestamp with time zone NOT NULL DEFAULT now(),
	CONSTRAINT "chat5_suggestions_id_pk" PRIMARY KEY("id")
);

-- Stream table (for streaming responses)
CREATE TABLE IF NOT EXISTS "chat5_streams" (
	"id" uuid DEFAULT gen_random_uuid() NOT NULL,
	"chatId" uuid NOT NULL,
	"createdAt" timestamp with time zone NOT NULL DEFAULT now(),
	CONSTRAINT "chat5_streams_id_pk" PRIMARY KEY("id")
);

-- Add foreign key constraints (referencing auth.users like your existing tables)
DO $$ BEGIN
 ALTER TABLE "chat5_chats" ADD CONSTRAINT "chat5_chats_userId_auth_users_id_fk" FOREIGN KEY ("userId") REFERENCES "auth"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_messages" ADD CONSTRAINT "chat5_messages_chatId_chat5_chats_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."chat5_chats"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_votes" ADD CONSTRAINT "chat5_votes_chatId_chat5_chats_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."chat5_chats"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_votes" ADD CONSTRAINT "chat5_votes_messageId_chat5_messages_id_fk" FOREIGN KEY ("messageId") REFERENCES "public"."chat5_messages"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_documents" ADD CONSTRAINT "chat5_documents_userId_auth_users_id_fk" FOREIGN KEY ("userId") REFERENCES "auth"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_suggestions" ADD CONSTRAINT "chat5_suggestions_userId_auth_users_id_fk" FOREIGN KEY ("userId") REFERENCES "auth"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_suggestions" ADD CONSTRAINT "chat5_suggestions_documentId_documentCreatedAt_chat5_documents_id_createdAt_fk" FOREIGN KEY ("documentId","documentCreatedAt") REFERENCES "public"."chat5_documents"("id","createdAt") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "chat5_streams" ADD CONSTRAINT "chat5_streams_chatId_chat5_chats_id_fk" FOREIGN KEY ("chatId") REFERENCES "public"."chat5_chats"("id") ON DELETE CASCADE ON UPDATE CASCADE;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chat5_messages_chatId_createdAt ON "chat5_messages" ("chatId", "createdAt");
CREATE INDEX IF NOT EXISTS idx_chat5_chats_userId_createdAt ON "chat5_chats" ("userId", "createdAt");
CREATE INDEX IF NOT EXISTS idx_chat5_documents_userId_createdAt ON "chat5_documents" ("userId", "createdAt");