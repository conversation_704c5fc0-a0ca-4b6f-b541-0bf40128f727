'use client';

import type { Attachment, UIMessage } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from './chat-header';
import type { Vote } from '../lib/db/schema';
import { fetcher, fetchWithErrorHandlers, generateUUID } from '../lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '../hooks/use-artifact';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from './sidebar-history';
import { toast } from './toast';
import { useAuth } from '@/components/auth-provider'; // Changed to root auth provider
import { useSearchParams, useRouter } from 'next/navigation';
import { useChatVisibility } from '../hooks/use-chat-visibility';
import { useAutoResume } from '../hooks/use-auto-resume';
import { ChatSDKError } from '../lib/errors';

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  autoResume,
}: {
  id: string; // This is the chatId
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  autoResume: boolean;
}) {
  const { user, loading: authLoading } = useAuth(); // Get user and auth loading state
  const { mutate } = useSWRConfig();
  const router = useRouter();

  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  console.log('Chat component: Using API endpoint /dashboard/chat5/api/chat');

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    experimental_resume,
    data,
  } = useChat({
    id,
    api: '/dashboard/chat5/api/chat', // Specify the correct API endpoint for chat5
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    fetch: fetchWithErrorHandlers,
    headers: user?.id ? { 'x-user-id': user.id } : {}, // Added x-user-id header
    experimental_prepareRequestBody: (body) => {
      const lastMessage = body.messages.at(-1);
      console.log('Chat: Preparing request body with message:', lastMessage);
      console.log('Chat: Full body object:', body);

      const requestBody = {
        id: id,
        message: lastMessage,
        selectedChatModel: initialChatModel,
        selectedVisibilityType: visibilityType,
      };

      console.log('Chat: Final request body being sent:', JSON.stringify(requestBody, null, 2));
      return requestBody;
    },
    onFinish: () => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));
    },
    onError: (error) => {
      if (error instanceof ChatSDKError) {
        toast({
          type: 'error',
          description: error.message,
        });
      }
    },
  });

  console.log('Chat component: Current status:', status, 'Messages count:', messages.length);

  const searchParams = useSearchParams();
  const query = searchParams.get('query');

  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);
  const [hasNavigated, setHasNavigated] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      append({
        role: 'user',
        content: query,
      });

      setHasAppendedQuery(true);
      // Only change URL if we're on the home page
      if (window.location.pathname === '/dashboard/chat5') {
        window.history.replaceState({}, '', `/dashboard/chat5/chat/${id}`);
      }
    }
  }, [query, append, hasAppendedQuery, id]);

  // Navigate to chat URL after first message is sent (only for new chats)
  useEffect(() => {
    // Only navigate if we're on the home page and have sent a message
    const currentPath = window.location.pathname;
    if (
      !hasNavigated &&
      currentPath === '/dashboard/chat5' &&
      initialMessages.length === 0 &&
      messages.length >= 2 &&
      status === 'ready'
    ) {
      setHasNavigated(true);
      router.push(`/dashboard/chat5/chat/${id}`);
    }
  }, [messages.length, initialMessages.length, status, hasNavigated, router, id]);

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/dashboard/chat5/api/vote?chatId=${id}` : null,
    fetcher,
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  useAutoResume({
    autoResume,
    initialMessages,
    experimental_resume,
    data,
    setMessages,
  });

  // Display a loading indicator while authentication is in progress or if user is not yet available
  if (authLoading) {
    return <div className="flex justify-center items-center h-full">Loading authentication...</div>;
  }

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={initialChatModel}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
        />

        <Messages
          chatId={id}
          status={status}
          votes={votes}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
        />

        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={handleSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
              selectedVisibilityType={visibilityType}
            />
          )}
        </form>
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />
    </>
  );
}
