import { Toaster } from 'sonner';
import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { ThemeProvider } from './components/theme-provider';
// import { AuthProvider } from './components/auth-provider'; // Removed: Rely on root AuthProvider
import <PERSON>ript from 'next/script';

import './globals.css';

export const metadata: Metadata = {
  metadataBase: new URL('https://chat.vercel.ai'),
  title: 'Next.js Chatbot Template',
  description: 'Next.js chatbot template using the AI SDK.',
};

export const viewport = {
  maximumScale: 1, // Disable auto-zoom on mobile Safari
};

const geist = Geist({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist',
});

const geistMono = Geist_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist-mono',
});

const LIGHT_THEME_COLOR = 'hsl(0 0% 100%)';
const DARK_THEME_COLOR = 'hsl(240deg 10% 3.92%)';
const THEME_COLOR_SCRIPT = `\
(function() {
  var html = document.documentElement;
  var meta = document.querySelector('meta[name="theme-color"]');
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('name', 'theme-color');
    document.head.appendChild(meta);
  }
  function updateThemeColor() {
    var isDark = html.classList.contains('dark');
    meta.setAttribute('content', isDark ? '${DARK_THEME_COLOR}' : '${LIGHT_THEME_COLOR}');
  }
  var observer = new MutationObserver(updateThemeColor);
  observer.observe(html, { attributes: true, attributeFilter: ['class'] });
  updateThemeColor();
})();`;

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // The className for fonts and suppressHydrationWarning should ideally be managed
  // by the root layout (app/layout.tsx) if these fonts and next-themes are global.
  // If they are specific to this section, consider applying them to a wrapper div.
  return (
    // Removed AuthProvider from here.
    // The ThemeProvider and Toaster can remain if they are specific to this layout's scope,
    // or they could also be managed by the root layout for consistency.
    // Assuming ThemeProvider here is intentional for this section for now.
    <div className={`${geist.variable} ${geistMono.variable} antialiased`}>
      <Script id="theme-color-script" strategy="lazyOnload">
        {THEME_COLOR_SCRIPT}
      </Script>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange={false} // Value from ThemeProvider component definition
      >
        <Toaster position="top-center" />
        {children}
      </ThemeProvider>
    </div>
  );
}
