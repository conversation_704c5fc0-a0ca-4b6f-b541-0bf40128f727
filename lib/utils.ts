import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const fetcher = (url: string) => fetch(url).then((res) => res.json());

export const fetchWithErrorHandlers = async (url: string, options?: RequestInit) => {
  // Add default headers
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'x-user-id': '2fbff9b7-8492-4068-b80d-df4d9a5d096d', // For demo purposes, use a valid UUID
  };

  const response = await fetch(url, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options?.headers,
    },
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || `HTTP error! status: ${response.status}`);
  }
  
  return response;
};
