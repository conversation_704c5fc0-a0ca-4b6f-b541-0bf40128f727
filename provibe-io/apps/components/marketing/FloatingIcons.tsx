// FloatingIcons.tsx
"use client";
import React, { useState, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { aiTools } from "@/data/ai-tools"; // Ensure this path is correct

// Split tools for left and right sides
const leftTools = aiTools.slice(0, Math.floor(aiTools.length / 2));
const rightTools = aiTools.slice(Math.floor(aiTools.length / 2));

// Helper for random numbers
const randomRange = (min: number, max: number) => Math.random() * (max - min) + min;

// --- Grid & Animation Configuration ---
const GRID_CONFIG = {
  verticalStartPercent: 20,   // Start icons 20% from the top
  verticalEndPercent: 60,     // End icons 60% from the top (Total 40% height)
  horizontalMarginPercent: 5, // Keep icons 5% from the absolute edge
  // Reduced width to increase space between columns within the 30% container
  // Was 25, reducing to ~16.67 to approximate 1.5x wider gaps (25 / 1.5 ≈ 16.67)
  // Let's use 17 for simplicity
  horizontalWidthPercent: 50, // Icons occupy the space from 5% to 22% edge (Total 17% width)
  // Minimum spacing between icon centers in percentage of the container dimension.
  // Adjust if needed based on the new width
  minHorizontalSpacingPercent: 15, // May need adjustment
  minVerticalSpacingPercent: 15,
};

const ANIMATION_CONFIG = {
  scaleMin: 0.5,
  scaleMax: 1.5,
  maxDriftX: 8,
  maxDriftY: 10,
  maxRotation: 7,
  durationMin: 14,
  durationMax: 22,
  delayMax: 6,
};
// --- End Configuration ---

// Define the structure for calculated properties
type IconAnimationProps = {
  key: string;
  initialStyle: {
    left?: string;
    right?: string;
    top: string;
    opacity: number;
    zIndex: number;
    transform: string;
  };
  animate: {
    y: number[];
    x: number[];
    rotate: number[];
    scale: number[];
  };
  transition: {
    duration: number;
    repeat: number;
    repeatType: "mirror" | "loop" | "reverse";
    ease: string;
    delay: number;
  };
  logo: string;
  alt: string;
};

export function FloatingIcons() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const iconProps = useMemo((): { left: IconAnimationProps[], right: IconAnimationProps[] } => {
    if (!isMounted) {
      return { left: [], right: [] };
    }

    const calculateSideProps = (tools: typeof aiTools, side: 'left' | 'right'): IconAnimationProps[] => {
      const numIcons = tools.length;
      if (numIcons === 0) return [];

      const {
        verticalStartPercent,
        verticalEndPercent,
        horizontalMarginPercent,
        horizontalWidthPercent, // Using the updated value (17)
        minHorizontalSpacingPercent,
        minVerticalSpacingPercent,
      } = GRID_CONFIG;

      const verticalSpacePercent = verticalEndPercent - verticalStartPercent; // 40%
      const horizontalSpacePercent = horizontalWidthPercent; // Now 17%

      // --- Dynamic Grid Calculation ---
      // Ensure at least 3 columns, but still try for square-ish layout
      let colsPerSide = Math.max(3, Math.floor(Math.sqrt(numIcons)));
      let numRows = Math.ceil(numIcons / colsPerSide);

      // Calculate actual spacing based on grid dimensions
      let verticalStep = verticalSpacePercent / numRows;
      let horizontalStep = horizontalSpacePercent / colsPerSide; // Step within the narrower 17%

      // Adjust grid if spacing is too small
      while ((verticalStep < minVerticalSpacingPercent || horizontalStep < minHorizontalSpacingPercent) && colsPerSide < numIcons) {
        colsPerSide++;
        numRows = Math.ceil(numIcons / colsPerSide);
        verticalStep = verticalSpacePercent / numRows;
        horizontalStep = horizontalSpacePercent / colsPerSide;
        if (horizontalStep < minHorizontalSpacingPercent && colsPerSide > 1) {
           colsPerSide--;
           numRows = Math.ceil(numIcons / colsPerSide);
           verticalStep = verticalSpacePercent / numRows;
           horizontalStep = horizontalSpacePercent / colsPerSide;
           break;
        }
      }
      // --- End Dynamic Grid Calculation ---

      // Add padding around the grid points within the available space
      const verticalPadding = verticalStep / 2;
      const horizontalPadding = horizontalStep / 2;

      return tools.map((tool, index) => {
        const row = Math.floor(index / colsPerSide);
        const col = index % colsPerSide;

        // Calculate position centered within its grid cell
        const topPercent = verticalStartPercent + verticalPadding + row * verticalStep;
        // Position calculation remains the same, but horizontalStep is now smaller relative to the container
        const horizontalPercentFromEdge = horizontalMarginPercent + horizontalPadding + col * horizontalStep;

        // Randomize animation parameters
        const duration = randomRange(ANIMATION_CONFIG.durationMin, ANIMATION_CONFIG.durationMax);
        const delay = randomRange(0, ANIMATION_CONFIG.delayMax);
        const yMovement = randomRange(-ANIMATION_CONFIG.maxDriftY, ANIMATION_CONFIG.maxDriftY);
        const xMovement = randomRange(-ANIMATION_CONFIG.maxDriftX, ANIMATION_CONFIG.maxDriftX);
        const rotation = randomRange(-ANIMATION_CONFIG.maxRotation, ANIMATION_CONFIG.maxRotation);
        const scalePattern = [1, ANIMATION_CONFIG.scaleMax, ANIMATION_CONFIG.scaleMin, 1];
        const opacity = randomRange(0.75, 1);
        const zIndex = index;

        return {
          key: `${side}-icon-${index}`,
          initialStyle: {
            ...(side === 'left' ? { left: `${horizontalPercentFromEdge}%` } : { right: `${horizontalPercentFromEdge}%` }),
            top: `${topPercent}%`,
            opacity: opacity,
            zIndex: zIndex,
            transform: 'scale(1)',
          },
          animate: {
            y: [0, yMovement, 0],
            x: [0, xMovement, 0],
            rotate: [0, rotation, 0],
            scale: scalePattern,
          },
          transition: {
            duration: duration,
            repeat: Infinity,
            repeatType: "mirror",
            ease: "easeInOut",
            delay: delay,
          },
          logo: tool.logo,
          alt: `${tool.name} logo`,
        };
      });
    };

    return {
      left: calculateSideProps(leftTools, 'left'),
      right: calculateSideProps(rightTools, 'right'),
    };
  }, [isMounted]);

  if (!isMounted) {
    return null;
  }

  return (
    <div className="pointer-events-none absolute inset-0 overflow-hidden">
      {/* Container for left-side icons (occupies 30% width) */}
      <div className="absolute left-0 top-0 h-full w-[30%] lg:block">
        {iconProps.left.map((props) => (
          <motion.div
            key={props.key}
            className="absolute"
            style={{ ...props.initialStyle, transformOrigin: 'center center' }}
            animate={props.animate}
            transition={props.transition}
          >
            <img
              src={props.logo}
              alt={props.alt}
              className="h-10 w-10 object-contain" // Base icon size
            />
          </motion.div>
        ))}
      </div>

      {/* Container for right-side icons (occupies 30% width) */}
      <div className="absolute right-0 top-0 h-full w-[30%] lg:block">
        {iconProps.right.map((props) => (
          <motion.div
            key={props.key}
            className="absolute"
            style={{ ...props.initialStyle, transformOrigin: 'center center' }}
            animate={props.animate}
            transition={props.transition}
          >
            <img
              src={props.logo}
              alt={props.alt}
              className="h-10 w-10 object-contain"
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
}
