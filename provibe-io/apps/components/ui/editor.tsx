// /components/ui/editor.tsx
import { useEffect, useState } from "react"
import { LexicalComposer } from "@lexical/react/LexicalComposer"
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin"
import { ContentEditable } from "@lexical/react/LexicalContentEditable"
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin"
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin"
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin"
import { ListPlugin } from "@lexical/react/LexicalListPlugin"
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin"
import { TRANSFORMERS } from "@lexical/markdown"

import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary"
import { HeadingNode, QuoteNode } from "@lexical/rich-text"
// --- This import path is standard and should work if @lexical/link is installed ---
import { LinkNode } from "@lexical/link"
// --- ---
import { ListItemNode, ListNode } from "@lexical/list"
import { CodeNode } from "@lexical/code"

import { cn } from "@/lib/utils"
import ToolbarPlugin from "./editor/toolbar-plugin"
import { UpdatePlugin } from "./editor/update-plugin"

export function Editor({
  value,
  onChange,
  className,
  placeholder = "Write something...",
  editable = true,
}: {
  value: string
  onChange: (value: string) => void
  className?: string
  placeholder?: string
  editable?: boolean
}) {
  const initialConfig = {
    namespace: "editor",
    editable,
    theme: {
      // ... (rest of your theme config)
      root: "p-0 outline-none",
      link: "text-primary underline",
      text: {
        bold: "font-bold",
        italic: "italic",
        code: "rounded-md bg-muted p-1 font-mono text-sm",
      },
      paragraph: "mb-2",
      heading: {
        h1: "text-3xl font-bold",
        h2: "text-2xl font-bold",
        h3: "text-xl font-bold",
        h4: "text-lg font-bold",
        h5: "text-base font-bold",
        h6: "text-sm font-bold",
      },
      list: {
        ul: "list-disc pl-6",
        ol: "list-decimal pl-6",
      },
      quote: "border-l-2 pl-4 italic",
      code: "rounded-md bg-muted p-1 font-mono text-sm",
    },
    onError: (error: Error) => {
      console.error(error)
    },
    // Ensure all necessary nodes are included
    nodes: [
      HeadingNode,
      QuoteNode,
      LinkNode, // Make sure LinkNode is here
      ListNode,
      ListItemNode,
      CodeNode,
    ],
  }

  return (
    <div className="border rounded-md">
      <LexicalComposer initialConfig={initialConfig}>
        {editable && (
          <div className="flex flex-wrap gap-1 p-2 border-b bg-muted/50">
            <ToolbarPlugin />
          </div>
        )}
        <div className="p-3 min-h-[200px] max-h-[600px] overflow-y-auto relative">
          <RichTextPlugin
            contentEditable={
              <ContentEditable
                className={cn(
                  "prose dark:prose-invert prose-sm sm:prose-base lg:prose-lg focus:outline-none max-w-full min-h-[180px]",
                  className
                )}
              />
            }
            placeholder={
              <div className="absolute top-[10px] left-[10px] text-muted-foreground pointer-events-none">
                {placeholder}
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <HistoryPlugin />
          {editable && <AutoFocusPlugin />}
          <LinkPlugin /> {/* Requires LinkNode */}
          <ListPlugin /> {/* Requires ListNode, ListItemNode */}
          <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
          <UpdatePlugin value={value} onChange={onChange} />
        </div>
      </LexicalComposer>
    </div>
  )
}

