// /components/ui/editor/update-plugin.tsx
import { useEffect, useState } from "react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
// Import Markdown conversion utilities
import {
  $convertFromMarkdownString,
  $convertToMarkdownString,
  TRANSFORMERS, // Use the same transformers as your MarkdownShortcutPlugin
} from '@lexical/markdown';
import { $getRoot } from "lexical";

export function UpdatePlugin({
  value, // This should consistently be Markdown
  onChange // This should expect Markdown
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  const [editor] = useLexicalComposerContext();
  // Flag to prevent feedback loop
  const [isHandlingUpdate, setIsHandlingUpdate] = useState(false);

  // --- Effect 1: Update Lexical editor when external `value` (Markdown) changes ---
  useEffect(() => {
    if (value !== undefined && editor && !isHandlingUpdate) {
      // console.log("UpdatePlugin: External value changed, updating editor.");
      setIsHandlingUpdate(true); // Set flag before update
      editor.update(() => {
        try {
          // Clear existing content
          $getRoot().clear();
          // Convert the incoming Markdown string to Lexical nodes
          $convertFromMarkdownString(value, TRANSFORMERS);
          // console.log("UpdatePlugin: Editor updated from external Markdown.");
        } catch (error) {
          console.error("Error converting Markdown to Lexical:", error);
          // Handle error, maybe clear editor or show message
          $getRoot().clear();
        } finally {
          // Use setTimeout to reset the flag *after* the current update cycle completes
          setTimeout(() => setIsHandlingUpdate(false), 0);
        }
      }, { tag: 'external' }); // Tag the update source
    }
  }, [editor, value]); // Removed isHandlingUpdate from deps

  // --- Effect 2: Update external state (`onChange`) when Lexical editor changes ---
  useEffect(() => {
    const removeUpdateListener = editor.registerUpdateListener(
      ({ editorState, dirtyElements, dirtyLeaves, prevEditorState, tags }) => {
        // Prevent updates triggered by the external value setting itself
        if (tags.has('external') || isHandlingUpdate) {
          // console.log("UpdatePlugin: Skipping update triggered by external source or during handling.");
          return;
        }

        // Check if the editor state actually changed meaningfully
        if (editorState.isEmpty() && prevEditorState.isEmpty()) {
           // console.log("UpdatePlugin: Skipping update, editor is empty and was empty.");
           return;
        }
        if (editorState.toJSON() === prevEditorState.toJSON()) {
           // console.log("UpdatePlugin: Skipping update, editor state JSON is identical.");
           return;
        }


        // console.log("UpdatePlugin: Editor changed internally, calling onChange.");
        setIsHandlingUpdate(true); // Set flag before reading/converting
        editorState.read(() => {
          try {
            // Convert the current Lexical state to a Markdown string
            const markdownString = $convertToMarkdownString(TRANSFORMERS);
            // console.log("UpdatePlugin: Converted to Markdown, length:", markdownString.length);
            onChange(markdownString); // Send Markdown back up
          } catch (error) {
            console.error("Error converting Lexical to Markdown:", error);
          } finally {
             // Use setTimeout to reset the flag *after* the current update cycle completes
             setTimeout(() => setIsHandlingUpdate(false), 0);
          }
        });
      }
    );

    // Cleanup function
    return () => {
      // console.log("UpdatePlugin: Cleaning up update listener.");
      removeUpdateListener();
    };
  }, [editor, onChange]); // Removed isHandlingUpdate from deps

  return null; // This plugin doesn't render anything
}
