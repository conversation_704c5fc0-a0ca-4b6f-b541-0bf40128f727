import { supabase } from '@/lib/supabase-client';
import { OpenAIStream, StreamingTextResponse } from 'ai';

// OpenRouter API interaction (similar to ai-service.ts)
const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

async function callOpenRouter({
  model,
  messages,
  temperature,
  maxTokens,
  jsonOutput,
  stream = false
}: {
  model: string;
  messages: Array<{ role: string; content: string }>;
  temperature?: number;
  maxTokens?: number;
  jsonOutput?: boolean;
  stream?: boolean;
}): Promise<any> {
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  if (!openRouterApiKey) throw new Error("OPENROUTER_API_KEY is not set.");

  const siteUrl = process.env.OPENROUTER_SITE_URL || "http://localhost:3000"; // Default or your actual site
  const appName = process.env.OPENROUTER_APP_NAME || "ProvibeInternal"; // Your app name

  const body: any = {
    model,
    messages,
    ...(temperature !== undefined && { temperature }),
    ...(maxTokens !== undefined && { max_tokens: maxTokens }), // Standard parameter for most models via OpenRouter
    ...(stream && { stream: true }),
  };

  // For OpenAI models (and potentially others that support it via OpenRouter passthrough)
  if (jsonOutput && (model.startsWith("openai/") || model.includes("gpt-"))) {
    body.response_format = { type: "json_object" };
  }
  // For Anthropic models that support JSON output via response_format
  if (jsonOutput && model.startsWith("anthropic/")) {
     // Anthropic's Claude 3.5+ models use response_format for JSON
    // For older ones, or if this doesn't work via OR, prompt engineering is the fallback.
    // We are simplifying here, assuming OR passes this through or model handles it.
    // If specific Anthropic JSON features are needed, prompt engineering is more robust.
    // body.response_format = { type: "json_object" }; // This might not be universally supported by OR for all Claude
    // Instead, ensure the system prompt for Claude requests JSON.
  }

  const response = await fetch(OPENROUTER_API_URL, {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${openRouterApiKey}`,
      "Content-Type": "application/json",
      "HTTP-Referer": siteUrl,
      "X-Title": appName,
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({})); // Catch if response is not JSON
    throw new Error(`OpenRouter API request failed with status ${response.status}: ${errorData.error?.message || response.statusText}`);
  }
  return stream ? response : response.json();
}

// Common interface for all AI providers
export interface AIProvider {
  generateContent(prompt: string, options?: any): Promise<string>;
  generateContentStream?(prompt: string, options?: any): Promise<ReadableStream<Uint8Array>>;
  supportsJsonOutput?: boolean;
}

export interface AIProviderOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  jsonOutput?: boolean;
  userId?: string;
  projectId?: string;
  taskSlug?: string; // Used as api_name for logging
  skipLogging?: boolean;
}

// OpenAI provider implementation
export class OpenAIProvider implements AIProvider {
  supportsJsonOutput = true;
  
  async generateContent(prompt: string, options: AIProviderOptions = {}): Promise<string> {
    let {
      model = "gpt-4o",
      temperature = 0.7,
      maxTokens = 1000,
      systemPrompt,
      jsonOutput = false,
      userId,
      projectId,
      taskSlug,
      skipLogging
    } = options;
    
    const openRouterModelId = model.startsWith("openai/") ? model : `openai/${model}`;

    console.log("OpenAIProvider (via OpenRouter) execution input", {
      userId, taskSlug, model: openRouterModelId, promptLength: prompt.length
    });

    const messages = systemPrompt 
      ? [
          { role: "system", content: systemPrompt },
          { role: "user", content: prompt }
        ]
      : [{ role: "user", content: prompt }];

    const startTime = Date.now();
    let apiSuccess = false;
    let errorMessage: string | null = null;
    let responseData: any = null;
    let content = "";

    try {
      responseData = await callOpenRouter({
        model: openRouterModelId,
        messages,
        temperature,
        maxTokens,
        jsonOutput,
      });
      
      content = responseData.choices[0]?.message?.content || "";
      if (!content && !jsonOutput) throw new Error("Empty response from OpenRouter for OpenAI model");
      if (jsonOutput && !content) content = JSON.stringify(responseData.choices[0]?.message || {}); // Ensure content for JSON

      apiSuccess = true;
      return content;
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : String(error);
      console.error("OpenRouter (OpenAIProvider) error:", errorMessage);
      throw error;
    } finally {
      const endTime = Date.now();
      const latency = endTime - startTime;
      const usage = responseData?.usage;

      if (userId && userId !== "unknown" && !skipLogging) {
        try {
          const { error: logError } = await supabase.from("prompt_executions").insert({
            user_id: userId,
            project_id: projectId || null,
            api_name: taskSlug || "openai_provider_unknown",
            model: responseData?.model || openRouterModelId,
            tokens_in: usage?.prompt_tokens || 0,
            tokens_out: usage?.completion_tokens || 0,
            cost: usage?.cost, // Log cost if available
            latency: latency,
            executed_at: new Date(startTime).toISOString(),
            success: apiSuccess,
            error_message: errorMessage,
          });
          if (logError) console.error("Supabase insert error (OpenAIProvider):", logError);
        } catch (logError) {
          console.error("Error logging prompt execution (OpenAIProvider):", logError);
        }
      }
    }
  }

  async generateContentStream(prompt: string, options: AIProviderOptions = {}): Promise<ReadableStream<Uint8Array>> {
    let {
      model = "gpt-4o",
      temperature = 0.7,
      maxTokens = 1000,
      systemPrompt
    } = options;

    const openRouterModelId = model.startsWith("openai/") ? model : `openai/${model}`;

    const messages = systemPrompt
      ? [{ role: "system", content: systemPrompt }, { role: "user", content: prompt }]
      : [{ role: "user", content: prompt }];

      try {
        const response = await callOpenRouter({
          model: openRouterModelId, messages, temperature, maxTokens, stream: true
        });
        // Use the Vercel AI SDK's OpenAIStream for parsing SSE from OpenRouter
        return OpenAIStream(response);
      } catch (error) {
        console.error("OpenRouter (OpenAIProvider) stream error:", error);
        throw error;
      }
  }
}

// Gemini provider implementation
export class GeminiProvider implements AIProvider {
  supportsJsonOutput = true;

  async generateContent(prompt: string, options: AIProviderOptions = {}): Promise<string> {
    const {
      model = "gemini-1.5-flash",
      temperature = 0.7,
      maxTokens,
      systemPrompt,
      jsonOutput = false,
      userId,
      projectId,
      taskSlug,
      skipLogging
    } = options;

    const openRouterModelId = model.startsWith("google/") ? model : `google/${model}`;

    console.log("GeminiProvider (via OpenRouter) execution input", {
      userId, taskSlug, model: openRouterModelId, promptLength: prompt.length
    });

    let fullPrompt = systemPrompt ? `${systemPrompt}\n\n${prompt}` : prompt;
    if (jsonOutput) {
      fullPrompt += "\n\nPlease format your response as a valid JSON object.";
    }

    // Gemini typically uses a single prompt, so we combine system and user into one user message.
    // Or, if OpenRouter handles system prompts for Gemini well, we can use that.
    // For simplicity, let's use the combined prompt approach for the user message.
    const messages = systemPrompt
      ? [{ role: "system", content: systemPrompt }, { role: "user", content: prompt }]
      : [{ role: "user", content: fullPrompt }];

    const startTime = Date.now();
    let apiSuccess = false;
    let errorMessage: string | null = null;
    let responseData: any = null;
    let content = "";
    
    try {
      responseData = await callOpenRouter({
        model: openRouterModelId,
        messages, // Send separate system/user prompts if OR handles it well for Gemini
        temperature,
        maxTokens,
        // jsonOutput is handled by prompt engineering for Gemini via OpenRouter
      });
      content = responseData.choices[0]?.message?.content || "";
      if (!content) throw new Error("Empty response from OpenRouter for Gemini model");
      apiSuccess = true;
      return content;
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : String(error);
      console.error("OpenRouter (GeminiProvider) error:", errorMessage);
      throw error;
    } finally {
      const endTime = Date.now();
      const latency = endTime - startTime;
      const usage = responseData?.usage;

      if (userId && userId !== "unknown" && !skipLogging) {
        try {
          const { error: logError } = await supabase.from("prompt_executions").insert({
            user_id: userId,
            project_id: projectId || null,
            api_name: taskSlug || "gemini_provider_unknown",
            model: responseData?.model || openRouterModelId,
            tokens_in: usage?.prompt_tokens || 0,
            tokens_out: usage?.completion_tokens || 0,
            cost: usage?.cost,
            latency: latency,
            executed_at: new Date(startTime).toISOString(),
            success: apiSuccess,
            error_message: errorMessage,
          });
          if (logError) console.error("Supabase insert error (GeminiProvider):", logError);
        } catch (logError) {
          console.error("Error logging prompt execution (GeminiProvider):", logError);
        }
      }
    }
  }

  async generateContentStream(prompt: string, options: AIProviderOptions = {}): Promise<ReadableStream<Uint8Array>> {
    const {
      model = "gemini-1.5-flash",
      temperature = 0.7,
      maxTokens,
      systemPrompt
    } = options;
    
    const openRouterModelId = model.startsWith("google/") ? model : `google/${model}`;

    const messages = systemPrompt
      ? [{ role: "system", content: systemPrompt }, { role: "user", content: prompt }]
      : [{ role: "user", content: prompt }]; // Or combined prompt for user message

    try {
      const response = await callOpenRouter({
        model: openRouterModelId, messages, temperature, maxTokens, stream: true
      });
      return OpenAIStream(response); // Assuming OpenRouter SSE is compatible
    } catch (error) {
      console.error("OpenRouter (GeminiProvider) stream error:", error);
      throw error;
    }
  }
}

export function getAIProvider(modelIdentifier: string): AIProvider {
  // modelIdentifier is the short form like "gpt-4o", "gemini-1.5-flash"
  // Providers will prepend "openai/", "google/", "anthropic/" as needed for OpenRouter
  if (modelIdentifier.startsWith('gpt-')) {
    return new OpenAIProvider();
  } else if (modelIdentifier.startsWith("gemini-")) {
    return new GeminiProvider();
  } else if (modelIdentifier.startsWith("claude-")) {
    return new ClaudeProvider();
  } else {
    console.warn(`Unrecognized model prefix: "${modelIdentifier}", defaulting to OpenAIProvider (using its default OpenRouter model).`);
    return new OpenAIProvider();
  }
}

// Add a Claude provider class
class ClaudeProvider implements AIProvider {
  supportsJsonOutput = true;

  async generateContent(prompt: string, options: AIProviderOptions = {}): Promise<string> {
    const {
      model = "claude-3-opus-20240229",
      temperature = 0.7,
      maxTokens = 1000,
      systemPrompt = "",
      jsonOutput = false,
      userId,
      projectId,
      taskSlug,
      skipLogging
    } = options;

    const openRouterModelId = model.startsWith("anthropic/") ? model : `anthropic/${model}`;
    
    console.log("ClaudeProvider (via OpenRouter) execution input", {
      userId, taskSlug, model: openRouterModelId, promptLength: prompt.length
    });

    let effectiveSystemPrompt = systemPrompt;
    if (jsonOutput) {
      // For Claude, robust JSON often requires explicit prompting,
      // even if response_format is attempted via OpenRouter.
      effectiveSystemPrompt = `${systemPrompt}\nPlease ensure your entire response is a single, valid JSON object. Do not include any text outside of the JSON structure.`;
    }

    const messages = [{ role: "user", content: prompt }];
    if (effectiveSystemPrompt) {
      messages.unshift({ role: "system", content: effectiveSystemPrompt });
    }

    const startTime = Date.now();
    let apiSuccess = false;
    let errorMessage: string | null = null;
    let responseData: any = null;
    let content = "";

    try {
      responseData = await callOpenRouter({
        model: openRouterModelId,
        messages,
        temperature,
        maxTokens,
        jsonOutput // Attempt to pass, but rely on prompt for Claude
      });

      content = responseData.choices[0]?.message?.content || "";
      if (!content) throw new Error("Empty response from OpenRouter for Claude model");
      apiSuccess = true;

      // Fallback logic for overloaded models (simplified for OpenRouter)
      // OpenRouter might handle some of this, or you might get specific errors.
      // This is a basic example; more sophisticated error handling might be needed.
      if (responseData.error && responseData.error.message && responseData.error.message.includes("Overloaded")) {
         console.warn(`Claude model ${openRouterModelId} reported as overloaded by OpenRouter.`);
         // Potentially implement a retry or fallback to a different Claude model via OpenRouter
         // For now, we'll let the error propagate if it wasn't caught by callOpenRouter
      }
      return content;

    } catch (error: any) {
      errorMessage = error instanceof Error ? error.message : String(error);
      console.error("OpenRouter (ClaudeProvider) error:", errorMessage);

      // Example of specific error handling for overload, if not caught by callOpenRouter
      if (errorMessage.includes("Overloaded") || (error?.status === 529 || error?.response?.status === 529)) {
        console.warn(`Claude model ${openRouterModelId} seems overloaded. Attempting fallback if configured.`);
        const fallbackModel = "anthropic/claude-3-haiku-20240307"; // Example fallback
        if (openRouterModelId !== fallbackModel) {
          console.log(`Falling back to ${fallbackModel}...`);
          return this.generateContent(prompt, { ...options, model: fallbackModel.split('/')[1] }); // Pass short model name
        } else {
          throw new Error("Claude API (via OpenRouter) is currently overloaded, and fallback also failed.");
        }
      }
      throw error;
    } finally {
      const endTime = Date.now();
      const latency = endTime - startTime;
      const usage = responseData?.usage;

      if (userId && userId !== "unknown" && !skipLogging) {
        try {
          const { error: logError } = await supabase.from("prompt_executions").insert({
            user_id: userId,
            project_id: projectId || null,
            api_name: taskSlug || "claude_provider_unknown",
            model: responseData?.model || openRouterModelId,
            tokens_in: usage?.prompt_tokens || 0,
            tokens_out: usage?.completion_tokens || 0,
            cost: usage?.cost,
            latency: latency,
            executed_at: new Date(startTime).toISOString(),
            success: apiSuccess,
            error_message: errorMessage,
          });
          if (logError) console.error("Supabase insert error (ClaudeProvider):", logError);
        } catch (logError) {
          console.error("Error logging prompt execution (ClaudeProvider):", logError);
        }
      }
    }
  }

  async generateContentStream(prompt: string, options: AIProviderOptions = {}): Promise<ReadableStream<Uint8Array>> {
    const {
      model = "claude-3-opus-20240229",
      temperature = 0.7,
      maxTokens = 1000,
      systemPrompt = ""
    } = options;

    const openRouterModelId = model.startsWith("anthropic/") ? model : `anthropic/${model}`;

    const messages = [{ role: "user", content: prompt }];
    if (systemPrompt) {
      messages.unshift({ role: "system", content: systemPrompt });
    }

    try {
      const response = await callOpenRouter({
        model: openRouterModelId, messages, temperature, maxTokens, stream: true
      });
      return OpenAIStream(response); // Assuming OpenRouter SSE is compatible
    } catch (error: any) {
      console.error("OpenRouter (ClaudeProvider) stream error:", error);
      throw error;
    }
  }
}
// Helper to fetch prompt from database
export async function fetchPromptTemplate(taskSlug: string, userId?: string): Promise<{
  template: string;
  model: string;
  temperature: number;
  maxTokens: number;
  cost: number | null; // Added cost
}> {
  // Get user's subscription tier
  let userTier = "free";
  if (userId) {
    const { data: userData } = await supabase
      .from("profiles")
      .select("subscription_tier")
      .eq("id", userId)
      .maybeSingle();
    
    userTier = userData?.subscription_tier || "free";
  }
  
  // Fetch prompt template with model options
  const { data: promptData, error: promptError } = await supabase
    .from("prompt_templates")
    .select("content, free_model, pro_model, enterprise_model, temperature, max_tokens, cost")
    .eq("task_slug", taskSlug)
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (promptError || !promptData?.content) {
    throw new Error(`Prompt template for task '${taskSlug}' not found or is empty.`);
  }

  // Select model based on user tier
  const modelKey = `${userTier}_model`;
  const model = (promptData as any)[modelKey] || promptData.free_model || "gpt-4o"; // Use short model name
  
  return {
    template: promptData.content,
    model,
    temperature: promptData.temperature || 0.7,
    maxTokens: promptData.max_tokens || 1000,
    cost: promptData.cost // Return the cost
  };
}
