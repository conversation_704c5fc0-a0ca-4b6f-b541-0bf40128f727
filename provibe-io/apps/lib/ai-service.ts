import { createClient } from '@supabase/supabase-js';

// Initialize Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY! // Needs service role for access to all fields
);

interface ProjectData {
  id: string;
  user_id: string; // Added for logging purposes
  name: string;
  idea: string;
  refined_idea?: string;
  projectPlan?: string; // If it's part of the project object itself
  product_details?: Record<string, any>; // Or a more specific type
  selected_tools?: string[];
}

/**
 * Builds the system prompt for document generation with Mermaid support
 */
function buildSystemPrompt(docType: string, systemPrompt: string): string {
  // Add Mermaid instructions for specific document types
  if (docType === 'user_flow') {
    return `${systemPrompt}
    
    IMPORTANT: Format the user flow as a Mermaid diagram. Use the following syntax:
    
    \`\`\`mermaid
    flowchart TD
      A[Start] --> B{Decision}
      B -->|Yes| C[Action 1]
      B -->|No| D[Action 2]
      C --> E[End]
      D --> E
    \`\`\`
    
    Make the diagram comprehensive but not overly complex. Use appropriate node shapes and clear labels.`;
  }
  
  if (docType === 'architecture') {
    return `${systemPrompt}
    
    IMPORTANT: Format the system architecture as a Mermaid diagram. Use the following syntax:
    
    \`\`\`mermaid
    flowchart LR
      Client[Client] --> API[API Gateway]
      API --> Auth[Auth Service]
      API --> Data[Data Service]
      Data --> DB[(Database)]
    \`\`\`
    
    Use appropriate node shapes for different components (databases, services, clients, etc).
    Make the diagram comprehensive but clear and well-organized.`;
  }
  
  return systemPrompt;
}

/**
 * Validates project access for document generation
 */
async function validateProjectAccess(
  projectId: string, 
  userId: string
): Promise<ProjectData> {
  const { data: project, error } = await supabase
    .from('projects')
    .select('*')
    .eq('id', projectId)
    .eq('user_id', userId)
    .single();

  if (error || !project) {
    throw new Error('Project not found or access denied');
  }

  return project as ProjectData;
}

/**
 * Generates document content using AI based on document type and project data
 */
export async function generateAIDocument(
  docType: string,
  projectId: string,
  userId: string,
  projectPlan?: string
): Promise<{
  content: string;
  model: string; // The model identifier used (e.g., "openai/gpt-4o")
  tokensIn: number;
  tokensOut: number;
  cost?: number; // OpenRouter provides cost information
}> {
  try {
    // Validate project access
    const project = await validateProjectAccess(projectId, userId);

    console.log(`Generating ${docType} document for project ${project.id} using OpenRouter.`);

    // Ensure docType is a string
    if (typeof docType !== 'string') {
      throw new Error(`Invalid document type: ${JSON.stringify(docType)}`);
    }

    const docMeta = await getDocTypeMeta(docType);
    if (!docMeta) throw new Error(`Unknown document type: ${docType}`);
    if (!docMeta.ai_prompt || !docMeta.system_prompt) throw new Error(`Prompt or system message missing for doc type: ${docType}`);

    const userPromptContent = buildUserPrompt(project, projectPlan, docMeta.ai_prompt);
    const systemPromptContent = buildSystemPrompt(docType, docMeta.system_prompt);
    
    // Use model from DB, fallback to environment variable, then to a hardcoded default.
    const modelIdentifier = docMeta.model_identifier || process.env.OPENROUTER_DEFAULT_MODEL || "google/gemini-flash-1.5-latest"; 
    console.log(`Using model for ${docType}: ${modelIdentifier}`);
    
    const result = await generateWithOpenRouter(
      userPromptContent, 
      systemPromptContent, 
      modelIdentifier,
      // Pass necessary IDs for logging
      { userId: project.user_id, projectId: project.id, docType: docType } 
    );

    return result;
  } catch (error) {
    console.error(`Error generating ${docType} document via OpenRouter:`, error);
    throw new Error(`Failed to generate ${docType} document: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fetch document prompt & system message from DB
 */
async function getDocTypeMeta(docType: string): Promise<{ ai_prompt: string | null, system_prompt: string | null, model_identifier: string | null } | null> {
  const { data, error } = await supabase
    .from('document_types')
    .select('ai_prompt, system_prompt, model_identifier')
    .eq('id', docType)
    .single();

  if (error) {
    console.error('Error fetching docType metadata:', error);
    return null;
  }

  return data;
}

/**
 * Construct user prompt by merging project info with the prompt template
 */
function buildUserPrompt(
  project: ProjectData, // Use the defined interface
  projectPlanFromArgs: string | undefined,
  promptTemplate: string
): string {
  const context = `
Project Name: ${project.name}
Project Idea: ${project.refined_idea || project.idea}
${projectPlanFromArgs ? `Project Plan: ${projectPlanFromArgs}` : ''}
${project.product_details ? `Product Details: ${JSON.stringify(project.product_details)}` : ''}
${project.selected_tools ? `Selected Tools: ${project.selected_tools.join(', ')}` : ''}
`;

  return `${context}\n${promptTemplate}`;
}

/**
 * Generate content using OpenRouter
 */
async function generateWithOpenRouter(
  prompt: string,
  systemPrompt: string,
  modelIdentifier: string, // e.g., "openai/gpt-4o", "google/gemini-1.5-flash-latest"
  logOptions: {
    userId: string;
    projectId: string;
    docType: string; // Used for api_name and prompt_template_id in logs
  }
): Promise<{
  content: string;
  model: string; // The model that was actually used (returned by OpenRouter)
  tokensIn: number;
  tokensOut: number;
  cost?: number; // OpenRouter provides cost directly
}> {
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  if (!openRouterApiKey) {
    throw new Error("OPENROUTER_API_KEY is not set in environment variables.");
  }

  const siteUrl = process.env.OPENROUTER_SITE_URL || "http://www.provibe.dev"; // Replace with your actual site URL
  const appName = process.env.OPENROUTER_APP_NAME || "Provibe"; // Replace with your app name

  const startTime = Date.now();
  let apiSuccess = false;
  let errorMessage: string | null = null;
  let responseData: any = null;
  let httpStatus: number | undefined;

  try {
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${openRouterApiKey}`,
        "Content-Type": "application/json",
        "HTTP-Referer": siteUrl,
        "X-Title": appName,
      },
      body: JSON.stringify({
        model: modelIdentifier,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: prompt },
        ],
      }),
    });

    httpStatus = response.status;
    responseData = await response.json();

    if (!response.ok) {
      errorMessage = responseData.error?.message || responseData.message || `OpenRouter API request failed with status ${response.status}`;
      console.error("OpenRouter API Error:", responseData);
      throw new Error(errorMessage);
    }

    apiSuccess = true;
    const content = responseData.choices[0]?.message?.content || "";
    const usage = responseData.usage;

    return {
      content,
      model: responseData.model || modelIdentifier,
      tokensIn: usage?.prompt_tokens || 0,
      tokensOut: usage?.completion_tokens || 0,
      cost: usage?.cost,
    };
  } catch (error) {
    if (error instanceof Error) {
      errorMessage = errorMessage || error.message; // Prefer API error message if available
    } else {
      errorMessage = "An unknown error occurred during OpenRouter call.";
    }
    console.error(`Error in generateWithOpenRouter for ${logOptions.docType}:`, errorMessage);
    throw new Error(errorMessage);
  } finally {
    const endTime = Date.now();
    const latency = endTime - startTime;
    const usage = responseData?.usage;

    const logEntry = {
      user_id: logOptions.userId,
      project_id: logOptions.projectId,
      api_name: `document_generation:${logOptions.docType}`, // More specific API name
      model: responseData?.model || modelIdentifier,
      tokens_in: usage?.prompt_tokens || 0,
      tokens_out: usage?.completion_tokens || 0,
      latency: latency,
      executed_at: new Date(startTime).toISOString(),
      success: apiSuccess,
      error_message: errorMessage,
      prompt_template_id: logOptions.docType, // Using docType as the reference to document_types
      document_id: null, // Document ID is not known at this stage
      // http_status: httpStatus, // Optional: if you add this column to your table
    };

    const { error: logError } = await supabase.from('prompt_executions').insert(logEntry);
    if (logError) {
      console.error("Failed to log prompt execution to Supabase:", logError);
    }
  }
}
