import { supabase } from '@/lib/supabase-client';

export interface ChatSession {
  id: string;
  title: string | null;
  created_at: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  created_at: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * Creates a new chat session
 */
export async function createChatSession(userId: string): Promise<ChatSession> {
  try {
    const response = await fetch('/api/chat/create-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-user-id': userId,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Create session error response:', errorText);
      throw new Error(errorText || 'Failed to create chat session');
    }

    const data = await response.json();
    return data.session || { id: data.sessionId, created_at: new Date().toISOString(), title: null };
  } catch (error) {
    console.error('Error in createChatSession:', error);
    throw error;
  }
}

/**
 * Fetches chat sessions for a user
 */
export async function getChatSessions(
  userId: string,
  limit = 20,
  offset = 0
): Promise<PaginatedResponse<ChatSession>> {
  const response = await fetch(`/api/chat/sessions?limit=${limit}&offset=${offset}`, {
    headers: {
      'x-user-id': userId,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch chat sessions');
  }

  const data = await response.json();
  return {
    data: data.sessions,
    total: data.total,
    limit: data.limit,
    offset: data.offset,
  };
}

/**
 * Fetches messages for a chat session
 */
export async function getChatMessages(
  userId: string,
  sessionId: string,
  limit = 50,
  offset = 0
): Promise<PaginatedResponse<ChatMessage>> {
  const response = await fetch(
    `/api/chat/messages?sessionId=${sessionId}&limit=${limit}&offset=${offset}`,
    {
      headers: {
        'x-user-id': userId,
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch chat messages');
  }

  const data = await response.json();
  return {
    data: data.messages,
    total: data.total,
    limit: data.limit,
    offset: data.offset,
  };
}

/**
 * Updates the title of a chat session
 */
export async function updateChatSessionTitle(
  userId: string,
  sessionId: string,
  title: string
): Promise<void> {
  const response = await fetch('/api/chat/update-title', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-user-id': userId,
    },
    body: JSON.stringify({ sessionId, title }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update chat session title');
  }
}

/**
 * Deletes a chat session
 */
export async function deleteChatSession(userId: string, sessionId: string): Promise<void> {
  try {
    const response = await fetch(`/api/chat/delete-session?sessionId=${sessionId}`, {
      method: 'DELETE',
      headers: {
        'x-user-id': userId,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Delete session error response:', errorText);
      try {
        // Try to parse as JSON if possible
        const errorJson = JSON.parse(errorText);
        throw new Error(errorJson.error || 'Failed to delete chat session');
      } catch (e) {
        // If parsing fails, use the raw text
        throw new Error(errorText || 'Failed to delete chat session');
      }
    }
  } catch (error) {
    console.error('Error in deleteChatSession:', error);
    throw error;
  }
}

/**
 * Saves a chat message to the database
 */
export async function saveChatMessage(
  userId: string, 
  sessionId: string, 
  role: 'user' | 'assistant', 
  content: string,
  agentId?: string | null,
  projectId?: string | null
): Promise<void> {
  try {
    const { error } = await supabase
      .from('chat_messages')
      .insert({
        user_id: userId,
        session_id: sessionId,
        role,
        content,
        agent_id: agentId || null,
        project_id: projectId || null
      });
      
    if (error) throw error;
  } catch (error) {
    console.error('Error saving chat message:', error);
    throw error;
  }
}
