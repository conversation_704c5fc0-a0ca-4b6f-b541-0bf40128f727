// lib/chat/slashcommands.ts
import { Briefcase, FileEditIcon, FilePlusIcon } from "lucide-react"
import type { ReactNode } from "react"
import React from "react"

export type SlashCommand = {
  id: string
  command: string
  label: string
  description: string
  icon: () => ReactNode
  action: () => void
  toolName?: string
  agentId?: string
}

export const getSlashCommands = (setActiveCommand: (cmd: SlashCommand) => void): SlashCommand[] => [
  {
    id: "create-project",
    command: "/create-project",
    label: "Create Project",
    description: "Guides you through setting up a new project with help from the AI wizard",
    icon: () => React.createElement(Briefcase, { className: "h-4 w-4 mr-2" }),
    action: function () {
      setActiveCommand(this);
      if (typeof window !== "undefined" && window.__triggerProjectWizardAgent) {
        window.__triggerProjectWizardAgent();
      } else {
        console.warn("Agent trigger function not defined");
      }
    },
  },
  {
    id: "edit-document",
    command: "/edit-document",
    label: "Edit Doc",
    description: "Open a side viewer to edit the selected document",
    icon: () => React.createElement(FileEditIcon, { className: "h-4 w-4 mr-2" }),
    action: function () { setActiveCommand(this) },
  },
  {
    id: "create-doc",
    command: "/create-doc",
    label: "Create Doc",
    description: "Generate a document from chat context (e.g. GTM, PRD, User Flow)",
    icon: () => React.createElement(FilePlusIcon, { className: "h-4 w-4 mr-2" }),
    toolName: "create_document",
    action: function () {
      setActiveCommand(this)
      if (typeof window !== "undefined" && window.__triggerTool) {
        window.__triggerTool(this.toolName)
      }
    },
  },
  {
    id: "edit-project",
    command: "/edit-project",
    label: "Edit Project",
    description: "Modify current project's settings and metadata",
    icon: () => React.createElement(Briefcase, { className: "h-4 w-4 mr-2" }),
    action: function () { setActiveCommand(this) },
  },
  {
    id: "research",
    command: "/research",
    label: "Research",
    description: "Enable research mode for deeper exploration",
    icon: () => React.createElement(FilePlusIcon, { className: "h-4 w-4 mr-2" }),
    action: function () { setActiveCommand(this) },
  },
  
]

export const getInlineChatCommands = (setActiveCommand: (cmd: SlashCommand) => void): SlashCommand[] => {
  const all = getSlashCommands(setActiveCommand);
  return all.filter(cmd =>
    ["create-doc", "edit-document"].includes(cmd.id)
  );
};
