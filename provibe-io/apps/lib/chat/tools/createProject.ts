// /Users/<USER>/Documents/Provibe-20250521/lib/chat/tools/createProject.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import crypto from 'crypto'; // Ensure crypto is available if not globally

export const createProjectTool = {
  type: 'function',
  function: {
    name: "create_project",
    description: "Create a new project in the database",
    parameters: {
      type: "object",
      properties: {
        title: { type: "string", description: "Project title" },
        description: { type: "string", description: "Optional project description" }
      },
      required: ["title"]
    },
    execute: async (context: { supabaseAdmin: SupabaseClient<Database>, userId: string }, args: { title: string; description?: string }) => {
      try {
        if (!context.userId) {
          throw new Error("User ID is missing, cannot create project.");
        }

        const { data: newProject, error } = await context.supabaseAdmin
          .from('projects')
          .insert({
            name: args.title,
            user_id: context.userId,
            description: args.description || null,
            idea: `Initial idea for ${args.title}`,
            status: 'draft'
          })
          .select()
          .single();

        if (error) {
          console.error("Error creating project in Supabase:", error);
          throw error;
        }

        return JSON.stringify({
          success: true,
          project: newProject
        });
      } catch (error: any) {
        return JSON.stringify({
          success: false,
          error: error.message
        });
      }
    }
  }
};