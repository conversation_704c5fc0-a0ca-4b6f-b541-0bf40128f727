// /Users/<USER>/Documents/Provibe-20250521/lib/chat/tools/echo.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

export const echoTool = {
  type: 'function',
  function: {
    name: "echo",
    description: "Echoes back the input string unchanged",
    parameters: {
      type: "object",
      properties: {
        text: { type: "string", description: "Text to echo back" },
      },
      required: ["text"],
    },
    execute: async (context: { supabaseAdmin: SupabaseClient<Database>, userId: string }, args: { text: string }) => {
      return args.text;
    },
  },
};