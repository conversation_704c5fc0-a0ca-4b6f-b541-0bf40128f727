// /Users/<USER>/Documents/Provibe-20250521/lib/chat/tools/index.ts
import { echoTool } from './echo';
import { createProjectTool } from './createProject';
import { editProjectTool } from './editProject';
import { createDocumentTool } from './createDocument';
import { updateDocumentContentTool } from './updateDocumentContent';

// Define the type for a tool object based on your structure
export type ProvibeTool = typeof echoTool; // Or a more generic type if structures vary significantly

export const tools: ProvibeTool[] = [
  echoTool,
  createProjectTool,
  editProjectTool,
  createDocumentTool,
  updateDocumentContentTool,
];