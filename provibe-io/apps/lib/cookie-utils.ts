/**
 * Utility functions for managing cookies
 */

import { getSupabaseCookieConfig } from './supabase-client'

/**
 * Sets the Supabase auth cookie manually
 * @param session The Supabase session object
 */
export function setSupabaseCookie(session: any) {
  if (!session || typeof window === 'undefined') return false;

  try {
    const cookieConfig = getSupabaseCookieConfig()

    console.log("Setting Supabase cookie with config:", cookieConfig);

    // Create the cookie value
    const cookieValue = encodeURIComponent(JSON.stringify({
      access_token: session.access_token,
      refresh_token: session.refresh_token,
      expires_at: session.expires_at,
      user: {
        id: session.user.id,
        email: session.user.email,
      }
    }));

    // Build cookie string with domain-specific configuration
    let cookieString = `${cookieConfig.name}=${session.access_token}; path=${cookieConfig.path}; max-age=${cookieConfig.lifetime}; SameSite=${cookieConfig.sameSite}`

    // For localhost/development, don't set domain
    // For production, only set domain if it matches current hostname
    if (cookieConfig.domain && !window.location.hostname.includes('localhost')) {
      const currentHost = window.location.hostname;
      // Only set domain if it matches exactly or is a subdomain
      if (currentHost === cookieConfig.domain || currentHost.endsWith('.' + cookieConfig.domain)) {
        cookieString += `; domain=${cookieConfig.domain}`
        console.log("Setting domain-specific cookie for:", cookieConfig.domain);
      } else {
        console.log("Skipping domain setting due to mismatch:", {
          configDomain: cookieConfig.domain,
          currentHost: currentHost
        });
      }
    }

    // Add secure flag if specified
    if (cookieConfig.secure) {
      cookieString += `; secure`
    }

    console.log("Setting cookie string:", cookieString);

    // Set the cookie
    document.cookie = cookieString;

    // Verify cookie was set
    const cookieSet = document.cookie.includes(cookieConfig.name);
    console.log("Cookie set successfully:", cookieSet);

    // If cookie setting failed, try a simpler approach
    if (!cookieSet) {
      console.log("Primary cookie failed, trying simplified approach...");
      const simpleCookieString = `${cookieConfig.name}=${session.access_token}; path=/; max-age=${cookieConfig.lifetime}; SameSite=lax`;
      document.cookie = simpleCookieString;
      const simpleSet = document.cookie.includes(cookieConfig.name);
      console.log("Simplified cookie set:", simpleSet);

      if (!simpleSet) {
        // Ultimate fallback: use a basic cookie name
        console.log("Using ultimate fallback cookie...");
        const fallbackName = "sb-auth-token";
        document.cookie = `${fallbackName}=${session.access_token}; path=/; max-age=28800; SameSite=lax`;
        return document.cookie.includes(fallbackName);
      }

      return simpleSet;
    }

    return cookieSet;
  } catch (error) {
    console.error("Error setting Supabase cookie:", error);

    // Ultimate fallback: try setting a basic cookie
    try {
      console.log("Exception fallback: setting basic cookie");
      document.cookie = `sb-auth-token=${session.access_token}; path=/; max-age=28800; SameSite=lax`;
      return document.cookie.includes('sb-auth-token');
    } catch (fallbackError) {
      console.error("All cookie setting attempts failed:", fallbackError);
      return false;
    }
  }
}

/**
 * Checks if the Supabase auth cookie exists
 */
export function hasSupabaseCookie() {
  if (typeof window === 'undefined') return false;

  try {
    const cookieConfig = getSupabaseCookieConfig()

    // Check for the configured cookie name first
    if (document.cookie.includes(cookieConfig.name)) {
      return true;
    }

    // Check for basic fallback cookie
    if (document.cookie.includes('sb-auth-token')) {
      return true;
    }

    // Check for any Supabase-related cookies as last resort
    // This handles production cases where cookie names might be auto-generated
    const hasAnySbCookie = document.cookie.includes('sb-') && document.cookie.includes('auth-token');
    const hasSupabaseCookie = document.cookie.includes('supabase');

    return hasAnySbCookie || hasSupabaseCookie;
  } catch (error) {
    console.error("Error checking Supabase cookie:", error);
    return false;
  }
}

/**
 * Clears the Supabase auth cookie
 */
export function clearSupabaseCookie() {
  if (typeof window === 'undefined') return;

  try {
    const cookieConfig = getSupabaseCookieConfig()

    // Build cookie string to clear the cookie
    let cookieString = `${cookieConfig.name}=; path=${cookieConfig.path}; max-age=0`

    // Add domain if specified
    if (cookieConfig.domain) {
      cookieString += `; domain=${cookieConfig.domain}`
    }

    document.cookie = cookieString;
  } catch (error) {
    console.error("Error clearing Supabase cookie:", error);
  }
}

/**
 * Gets the current cookie configuration for debugging
 */
export function getCookieDebugInfo() {
  if (typeof window === 'undefined') return null;

  try {
    const cookieConfig = getSupabaseCookieConfig()
    return {
      cookieConfig,
      cookieExists: hasSupabaseCookie(),
      allCookies: document.cookie
    };
  } catch (error) {
    console.error("Error getting cookie debug info:", error);
    return null;
  }
}