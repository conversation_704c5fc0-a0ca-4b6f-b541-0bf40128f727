// Database schema types for the Provibe Lite project

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  subscription: "free" | "pro"
  credits_remaining: number
  projects_limit: number
}

export interface Project {
  id: string
  user_id: string
  name: string
  created_at: Date
  updated_at: Date
  status: "draft" | "completed" | "generating_docs"

  // Step 1: Idea
  idea: string
  refined_idea?: string
  voice_note_url?: string

  // Step 2: Tools
  selected_tools: string[]

  // Step 3: Details
  product_details: Record<string, string>
  clarifying_questions?: Record<string, any>
  last_creation_step?: number

  // Step 4: Plan
  project_plan?: string
}

export interface ProjectDocument {
  id: string
  project_id: string
  title: string
  type: "prd" | "user_flow" | "architecture" | "schema" | "api_spec"
  content: string
  created_at: Date
  updated_at: Date
  status: "pending" | "generating" | "completed" | "error"
  error_message?: string
}

export interface CreditUsageLog {
  id: string
  user_id: string
  project_id: string | null
  document_id: string | null
  action: "idea_refinement" | "ai_answer" | "plan_generation" | "document_generation" | "document_regeneration"
  credits_used: number
  created_at: Date
}

export interface AITool {
  id: string
  name: string
  category?: string
  logo: string
  description: string
  created_at: Date
  updated_at: Date
}

export interface DocumentType {
  id: string
  title: string
  icon: string
  cost: number
  description: string
  active: boolean
  ai_prompt: string
  system_prompt: string
  display_order?: number
  model_identifier?: string | null;
}

export interface PromptTemplate {
  id: string
  task_slug: string
  task_name: string
  content: string
  free_model: string
  pro_model: string
  enterprise_model: string
  max_tokens: number
  temperature: number
  top_p: number
  created_at: Date
  updated_at: Date
  cost?: number
}

export interface PromptExecution {
  id: string
  user_id: string
  project_id: string | null
  api_name: string
  model: string
  tokens_in: number
  tokens_out: number
  latency: number
  executed_at: Date
  success: boolean
  error_message: string | null
  prompt_template_id: string | null
  document_id: string | null
}
// ...

// Supabase table names
export const TABLES = {
  PROFILES: "profiles",
  PROJECTS: "projects",
  PROJECT_DOCUMENTS: "project_documents",
  CREDIT_USAGE_LOG: "credit_usage_log",
  AI_TOOLS: "ai_tools",
  DOCUMENT_TYPES: "document_types",
  PROMPT_TEMPLATES: "prompt_templates",
  PROMPT_EXECUTIONS: "prompt_executions"

}
