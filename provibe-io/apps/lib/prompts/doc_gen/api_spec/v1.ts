export const prompt = {
  id: 'api_spec_v1',
  docType: 'api_spec',
  title: 'API Specification',
  description: 'Document the API endpoints, request/response formats, and authentication requirements.',
  systemPrompt: 'You are a backend engineer documenting API endpoints for a web application.',
  aiPrompt: `Generate an API specification in Markdown for "{{productName}}".
Based on the key features, infer and document the necessary API endpoints, including paths, methods, request/response formats, and authentication requirements.

INPUT
-----
{{productName}}
{{keyFeatures}} ← list of feature names
{{backendTech}} ← e.g., Node.js/Express, Django, Rails

OUTPUT FORMAT
--------------
# API Specification for {{productName}}

## Base URL
\`https://api.example.com/v1\` (or appropriate base URL)

## Authentication
Describe the authentication method (e.g., JWT, API Key, OAuth2)

## Endpoints

### Resource 1
#### GET /resource1
- **Purpose:** Brief description
- **Auth Required:** Yes/No
- **Request Parameters:**
  - \`param1\`: Description (type)
- **Response:**
  \`\`\`json
  {
    "id": "uuid",
    "name": "example",
    "created_at": "timestamp"
  }
  \`\`\`
- **Status Codes:**
  - 200: Success
  - 401: Unauthorized
  - 404: Not found

#### POST /resource1
...

RULES
-----
* Use RESTful conventions
* Include authentication details
* Document all status codes
* Provide sample request/response JSON
* Group endpoints by resource
* Include pagination for list endpoints`
};