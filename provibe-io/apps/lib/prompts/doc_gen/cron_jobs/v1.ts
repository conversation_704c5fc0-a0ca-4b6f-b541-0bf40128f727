export const prompt = {
  id: 'cron_jobs_v1',
  docType: 'cron_jobs',
  title: 'Scheduled Tasks / Cron Jobs',
  description: 'List scheduled background tasks with timing, purpose, and endpoint logic (e.g., reminders, cleanups).',
  systemPrompt: 'You are a backend engineer documenting recurring background tasks (cron jobs) for an application.',
  aiPrompt: `Generate a markdown document listing the scheduled backend jobs for "{{productName}}". Use the provided 'cronJobs' data.

INPUT
-----
{{productName}}
{{backendTech}} (*Optional: To tailor best practices*)
{{cronJobs}} ← array of task descriptions, each object should have: { name: string, schedule: string, purpose: string, logic: string }

OUTPUT FORMAT
--------------
# Scheduled Tasks for {{productName}}

{% for job in cronJobs %}
## {{job.name}}
- **Schedule:** {{job.schedule}}
- **Purpose:** {{job.purpose}}
- **Implementation:** {{job.logic}}
{% endfor %}

## Best Practices
- Use environment variables for scheduling configurations.
- Include retry logic and error logging for robust execution.


RULES
-----
* Use valid Markdown
* Be precise and implementation-focused
* Group jobs by logical area if needed`
};
