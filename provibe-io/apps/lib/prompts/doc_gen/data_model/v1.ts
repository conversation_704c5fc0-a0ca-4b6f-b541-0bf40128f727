export const prompt = {
  id: 'data_model_v1',
  docType: 'data_model',
  title: 'High-Level Data Model',
  description: 'Explain the primary data entities, their attributes, and relationships in a conceptual model.',
  systemPrompt: 'You are a database designer tasked with creating a high-level conceptual data model.',
  aiPrompt: `Generate a conceptual data model in Markdown format for "{{productName}}", suitable for a {{database}} database.
Infer the necessary tables and their core fields based on the key features. Include standard fields like IDs and foreign keys.

INPUT  
{{productName}}
{{keyFeatures}}  ← List of feature names
{{database}}     ← e.g., PostgreSQL, MySQL

OUTPUT  (Markdown)
------------------
# Data Models ({{database}})

| Table       | Core Fields                                   | Notes                                    |
|-------|-------------|
| users | id UUID PK, email unique |
{% for f in keyFeatures %}
| {{f|snake}} | id UUID PK, userId FK users(id), ... |
{% endfor %}

RULES
-----
* Output valid Markdown.
* Do not generate full SQL DDL; list table names and essential fields only (PKs, FKs, key attributes).
* Use snake_case for table names.
* Assume UUIDs for primary keys (PK) and appropriate foreign keys (FK) referencing other tables (e.g., userId FK users(id)).`
};
