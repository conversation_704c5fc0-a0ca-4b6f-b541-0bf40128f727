export const prompt = {
  id: 'deployment_guide_v1',
  docType: 'deployment_guide',
  title: 'Deployment Guide',
  description: 'Provide a step-by-step deployment process including build, hosting, environment setup, and monitoring.',
  systemPrompt: 'You are a DevOps engineer creating a deployment guide for a web application.',
  aiPrompt: `Generate a concise deployment guide in Markdown for "{{productName}}".

INPUT
-----
{{productName}}
{{frontendTech}}
{{backendTech}}
{{hostingProvider}} ← e.g., Vercel, Railway
{{envVars}} ← list of required env keys
{{monitoringTools}} ← e.g., LogRocket, Sentry

OUTPUT FORMAT
--------------
# Deployment Guide for {{productName}}

## 1. Build Instructions
- Frontend ({{frontendTech}}): Provide the typical build command (e.g., \`npm run build\`) and specify the output directory (e.g., \`dist\`, \`.next\`).
- Backend ({{backendTech}}): Outline build/packaging steps if necessary (e.g., Docker build, dependency installation).

## 2. Hosting Setup
- Provider: {{hostingProvider}}
- Describe the recommended deployment method (e.g., Git integration, CLI push, manual upload).

## 3. Environment Variables
{% for var in envVars %}
- {{var.name}}: {{var.purpose}}
{% endfor %}

## 4. Monitoring & Observability
- Briefly describe how to integrate {{monitoringTools}} (e.g., add API key to env vars, install SDK).

## 5. Post-Deployment Checklist
- Smoke test checklist
- Fallback/recovery tips

RULES
-----
* Be concise and developer-focused
* Use valid Markdown
* Avoid marketing or fluff`
};
