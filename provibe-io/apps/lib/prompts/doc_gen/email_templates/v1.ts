export const prompt = {
    id: 'email_templates_v1',
    docType: 'email_templates',
    title: 'Transactional Email Templates',
    description: 'Generate sample email templates for user signup, password reset, notifications, and transactional flows.',
    systemPrompt: 'You are a copywriter specializing in transactional emails. Write concise and user-friendly email templates.',
    aiPrompt: `Generate concise, user-friendly transactional email templates in Markdown for "{{productName}}".
These templates should cover common scenarios like user signup and password resets, as well as potential notifications related to the product's core functionality.

INPUT
-----
{{productName}}
{{keyFeatures}}      ← List of feature names to understand potential notification triggers
{{targetAudience}}   ← To tailor the tone

OUTPUT FORMAT
--------------
# Transactional Email Templates for {{productName}}

## 1. Welcome / Signup Confirmation
- **Subject:** Welcome to {{productName}}!
- **Body:**
  Hi [User Name],

  Welcome aboard! We're excited to have you join {{productName}}.

  Get started by [Suggest a first action, e.g., creating your first project, exploring features].

  Best,
  The {{productName}} Team

## 2. Password Reset Request
- **Subject:** Reset your {{productName}} password
- **Body:**
  Hi [User Name],

  Someone requested a password reset for your {{productName}} account. If this was you, click the link below to set a new password. If not, you can safely ignore this email.

  [Password Reset Link]

  This link will expire in [Timeframe, e.g., 1 hour].

  Thanks,
  The {{productName}} Team

## 3. [Example Notification based on a Key Feature, e.g., Document Ready]
- **Subject:** Your [Feature-related item, e.g., Document] is Ready!
- **Body:**
  Hi [User Name],

  Good news! Your [Feature-related item] for [Context, e.g., Project X] is ready in {{productName}}.

  You can view it here: [Link to item]

  Best,
  The {{productName}} Team

RULES
-----
* Use valid Markdown.
* Keep emails concise, friendly, and focused on the primary action.
* Use placeholders like \`[User Name]\`, \`[Link]\`, \`[Password Reset Link]\`, etc., for dynamic content.`
};