export const prompt = {
  id: 'feature_list_v1',
  docType: 'feature_list',
  title: 'Feature List / Roadmap',
  description: 'Summarize all planned features for the app, organized by MVP, v1, v2 phases with priority tags.',
  systemPrompt: 'You are a product strategist creating a feature list and roadmap.',
  aiPrompt: `Generate a feature list in Markdown for "{{productName}}".
Organize the provided key features into logical MVP, v1, and v2 milestones. Prioritize core functionality for <PERSON>. For each feature, add a brief description and its primary user benefit.

INPUT
-----
{{productName}}
{{keyFeatures}}  ← List of feature names

OUTPUT FORMAT
--------------
# Feature List for {{productName}}

## MVP
- Feature A: Description and benefit
{% for feature in keyFeatures if feature is MVP %} - {{ feature }}: [Generate description and benefit] {% endfor %}

## v1
- Feature C: Description and benefit
{% for feature in keyFeatures if feature is v1 %} - {{ feature }}: [Generate description and benefit] {% endfor %}

## v2
- Feature E: Description and benefit
{% for feature in keyFeatures if feature is v2 %} - {{ feature }}: [Generate description and benefit] {% endfor %}

RULES
-----
* Each feature must include a 1-line benefit
* Organize by phases (MVP, v1, v2)
* Write in Markdown format`
};
