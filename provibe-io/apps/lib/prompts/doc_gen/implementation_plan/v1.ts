export const prompt = {
  id: 'implementation_plan_v1',
  docType: 'implementation_plan',
  title: 'Implementation Plan',
  description: 'Provide a step-by-step implementation plan to build, hosting, environment setup, deploy and monitor the app',
  systemPrompt: 'You are a highly organized software architect and project planner, capable of breaking down a project into logical, actionable implementation steps suitable for a development team, including AI coding agents.',
  aiPrompt: `Generate a detailed step-by-step Implementation Plan in Markdown for "{{productName}}" in the {{selectedTools}} environment.
Break down the project implementation into logical phases and actionable steps, considering the provided key features and technology stack. This plan should be detailed enough for a development team, including AI coding agents, to follow to build the project from scratch without errors.

INPUT VARS
-----------\n
{{productName}}
{{keyFeatures}} ← list of feature names
{{backendTech}} ← e.g., Node.js/Express, Django, Rails
{{frontendTech}} ← e.g., React
{{selectedTools}} ← e.g., v0, Lovable, Cursor, VSCode 

RULES (Read Carefully)
----------------------\n
1. Output in **valid Markdown format only**.
2. Structure the plan with clear headings for each major phase.
3. Use numbered lists for individual steps within each phase.
4. Ensure steps are actionable and consider the specified technologies.
5. Assume a standard agile development workflow.
6. Do not include any debugging information, JSON data, or metadata.

OUTPUT FORMAT
--------------\n
# Implementation Plan: {{productName}}

## 1. Project Setup & Environment
- Initialize the project repository.
- Set up the development environment (e.g., Node.js, Python, etc.).
- Configure environment variables for development.
- Set up linting and formatting tools (e.g., ESLint, Prettier).

## 2. Backend Implementation ({{backendTech}})
- Design the database schema (if applicable, considering {{database}}).
- Set up the backend framework (e.g., Express app, Django project).
- Implement authentication and authorization.
{% for feature in keyFeatures %}\n
- Develop API endpoints for the "{{feature}}" feature.
- Implement backend logic and data interactions for "{{feature}}".
{% endfor %}\n
- Write unit and integration tests for backend endpoints.

## 3. Frontend Implementation ({{frontendTech}})
- Set up the frontend framework/library (e.g., Create React App, Next.js project).
- Configure routing.
- Develop core UI components.
{% for feature in keyFeatures %}\n
- Implement the user interface for the "{{feature}}" feature.
- Integrate frontend with backend APIs for "{{feature}}".
{% endfor %}\n
- Write unit and end-to-end tests for frontend components and flows.

## 4. Database Implementation (If applicable, considering {{database}})
- Set up the database instance.
- Run migrations to create tables/collections based on the schema design.
- Implement database seeding for development/testing data.

## 5. Testing Strategy
- Define a comprehensive testing suite (Unit, Integration, E2E).
- Set up a continuous integration (CI) pipeline to run tests on pushes/pull requests.
- Define criteria for UAT (User Acceptance Testing).

## 6. Deployment Plan (Considering {{hostingPlatform}})
- Choose a hosting provider (if not already specified).
- Set up production environment variables on the hosting platform.
- Configure a CI/CD pipeline for automated deployments from version control.
- Set up domain and SSL certificate.
- Plan for database migration and setup in the production environment.

## 7. Monitoring & Maintenance
- Implement logging and error tracking.
- Set up application performance monitoring (APM).
- Plan for regular security audits and updates.
- Define a backup strategy for the database and application data.

## 8. Future Considerations
- Briefly mention potential future features or improvements.'
};

