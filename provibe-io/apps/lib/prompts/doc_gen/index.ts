import { prompt as prd_v1 } from './prd/v1';
import { prompt as user_flow_v1 } from './user_flow/v1';
import { prompt as architecture_v1 } from './architecture/v1';
import { prompt as api_spec_v1 } from './api_spec/v1';
import { prompt as schema_v1 } from './schema/v1';
import { prompt as data_model_v1 } from './data_model/v1';
import { prompt as feature_list_v1 } from './feature_list/v1';
import { prompt as integration_guide_v1 } from './integration_guide/v1';
import { prompt as prompt_templates_v1 } from './prompt_templates/v1';
import { prompt as test_plan_v1 } from './test_plan/v1';
import { prompt as sample_data_v1 } from './sample_data/v1';
import { prompt as email_templates_v1 } from './email_templates/v1';
import { prompt as deployment_guide_v1 } from './deployment_guide/v1';
import { prompt as ui_flows_v1 } from './ui_flows/v1';
import { prompt as wireframe_v1 } from './wireframe/v1';
import { prompt as roles_matrix_v1 } from './roles_matrix/v1';
import { prompt as cron_jobs_v1 } from './cron_jobs/v1';
import { prompt as implementation_plan_v1 } from './implementation_plan/v1';

// import more as needed...


export const PROMPT_TEMPLATES = {
  [prd_v1.id]: prd_v1,
  [user_flow_v1.id]: user_flow_v1,
  [architecture_v1.id]: architecture_v1,
  [api_spec_v1.id]: api_spec_v1,
  [schema_v1.id]: schema_v1,
  [data_model_v1.id]: data_model_v1,
  [feature_list_v1.id]: feature_list_v1,
  [integration_guide_v1.id]: integration_guide_v1,
  [prompt_templates_v1.id]: prompt_templates_v1,
  [test_plan_v1.id]: test_plan_v1,
  [sample_data_v1.id]: sample_data_v1,
  [email_templates_v1.id]: email_templates_v1,
  [deployment_guide_v1.id]: deployment_guide_v1,    
  [ui_flows_v1.id]: ui_flows_v1,
  [wireframe_v1.id]: wireframe_v1,
  [roles_matrix_v1.id]: roles_matrix_v1,
  [cron_jobs_v1.id]: cron_jobs_v1,
  [implementation_plan_v1.id]: implementation_plan_v1,

  // Add more as needed
  
};