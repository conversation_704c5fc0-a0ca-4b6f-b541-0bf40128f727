export const prompt = {
    id: 'integration_guide_v1',
    docType: 'integration_guide',
    title: 'Third-Party Integration Guide',
    description: 'Describe integrations with external tools or services, including authentication, data exchange, and error handling.',
    systemPrompt: 'You are a solutions architect documenting steps for integrating third-party services.',
    aiPrompt: `Generate a Markdown guide for integrating "{{productName}}" with the specified third-party services. Detail the purpose, authentication, APIs, error handling, and security considerations for each.
  
  INPUT
  -----
  {{productName}}
  {{keyFeatures}}       ← list of feature names
  {{integrations}}      ← list of objects, each like: { name: string, purpose: string, authMechanism: string, apisUsed: string }
  
  OUTPUT FORMAT
  --------------
  # Integration Guide for {{productName}}
  
  ## 1. Overview
  Brief summary of integrations planned and their role.
  
  ## 2. Service-by-Service Integration
  {% for integration in integrations %}
  ### {{integration.name}}
  - **Purpose:** What this integration enables
  - **Auth Mechanism:** {{integration.authMechanism}} (e.g., API Key, OAuth 2.0)
  - **APIs Used:** {{integration.apisUsed}} (List key endpoints or link to docs)
  - **Error Handling:** Describe common errors (e.g., 401 Unauthorized, 429 Rate Limit) and suggested retry/fallback logic.
  {% endfor %}
  
  ## 3. Security & Permissions
  - Token handling
  - Expiry and refresh
  - Role-based scopes (if any)
  
  ## 4. Fallbacks & Offline Modes
  - What happens if integration fails
  - How the UI communicates and recovers
  
  RULES
  -----
  - Use valid Markdown
  - Be implementation-ready
  - No filler or marketing copy`
  };
  