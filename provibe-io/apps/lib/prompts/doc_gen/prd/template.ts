export const prdTemplate = {
    id: 'prd_template_v1',
    title: 'Product Requirements Document – {{productName}}',
    markdown: `# Product Requirements – {{productName}}
  **Version:** 1.0  |  **Date:** {{currentDate}}
  
  ## 1. Vision & Goals
  - **Target Persona:** {{targetAudience}}
  - **USPs:** {{usp}}
  
  ## 2. Tech Snapshot
  - **Frontend:** {{frontendTech}}  
  - **Backend:** {{backendTech}}  
  - **Database:** {{database}}
  
  ## 3. Detailed Features  
  _Repeat the following block for each item in \`keyFeatures\`_
  
  ### {{featureName}}
  - **User Story:** As a {{targetAudience}}, I want ... so that ...
  - **UI Components:** 
    - Component 1
    - Component 2
  - **Happy Path Flow:**  
    1. Step one  
    2. Step two  
    3. ...
  - **Business Rules / Validation:**  
    - Rule A  
    - Rule B
  - **Acceptance Criteria:**  
    - GIVEN ... WHEN ... THEN ...
  
  ## 4. Key Assumptions (from Clarifying Questions)
  {% for q in clarifyingQuestions %}
  - **Q:** {{q.question}}  
    **A:** {{q.suggestedAnswer}} _(Dimension: {{q.dimension}})_
  {% endfor %}
  
  ## 5. Non-Functional Requirements
  - Performance  
  - Security  
  - Accessibility  
  - Error handling
  
  ## 6. Companion Specs
  | Document         | Purpose                  |
  |------------------|--------------------------|
  | \`userflow.md\`    | Screen-to-screen UX      |
  | \`architecture.md\`| Stack & data flow        |
  | \`api_spec.md\`    | Endpoint contracts       |
  | \`data_model.md\`  | DB tables and relations  |
  
  ## 7. Out-of-Scope (v1)
  - Feature X  
  - Feature Y
  
  ## 8. Open Questions / Future Work
  - [Needs Clarification: e.g., integration method with Vercel v0]`
  };
  