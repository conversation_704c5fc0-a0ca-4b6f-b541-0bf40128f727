export const prompt = {
  id: 'prd_v1',
  docType: 'prd',
  title: 'Product Requirements Document',
  description: 'Generate a detailed product requirements document that outlines the problem, target users, core features, and functional expectations of the application.',
  systemPrompt: 'You are a senior product manager creating a detailed Product Requirements Document (PRD) suitable for guiding development, including AI coding agents.',
  aiPrompt: `Generate a Product Requirements Document (PRD) in Markdown for "{{productName}}".
Follow the structure defined below precisely. Populate all sections based on the provided inputs. For the "Detailed Features" section, iterate through each item in \`keyFeatures\` and generate specific User Stories, UI Components, Happy Path Flows, Business Rules, and Acceptance Criteria relevant to that feature. Infer reasonable details where necessary but use "[Needs Clarification: ...]" for significant ambiguities. Assume Vision and Goals should be inferred if not explicitly provided.

INPUT VARS
-----------
{{productName}}  
{{refinedIdea}}  
{{targetAudience}}  
{{usp}}  
{{keyFeatures}}            ← List of feature names. Properly number the features.  
{{clarifyingQuestions}}    ← Array of {question, suggestedAnswer, dimension}
{{frontendTech}} (*For Tech Snapshot*)
{{backendTech}} (*For Tech Snapshot*)
{{database}} (*For Tech Snapshot*)
{{currentDate}}

RULES (Read Carefully)
----------------------
1. Output in **valid Markdown format only**.  
2. Use \`[Needs Clarification: ...]\` for any significant ambiguities or missing information that cannot be reasonably inferred.
3. Keep paragraphs ≤ 4 lines. Use bullet lists where suitable.
4. Do not include any debugging information, JSON data, or metadata in your response.
5. End your document with the "Open Questions / Future Work" section. Do not add any additional content after this section.

# Product Requirements – {{productName}}
**Version:** 1.0  |  **Date:** {{currentDate}}

## 1. Vision & Goals
- **Vision:** [Needs Clarification]
- **Goals:** [Needs Clarification]
- **Target Persona:** {{targetAudience}}
- **USPs:** {{usp}}

## 2. Tech Snapshot
- **Frontend:** {{frontendTech}}  
- **Backend:** {{backendTech}}  
- **Database:** {{database}}

## 3. Detailed Features  
{% for feature in keyFeatures %}

### {{loop.index}}. {{feature}}
- **User Story:** As a {{targetAudience}}, I want [Action related to '{{feature}}'] so that [Benefit related to '{{feature}}'].
- **UI Components:**
  - [List specific UI elements needed for '{{feature}}', e.g., Button, Input Field, Modal, Table]
  - [Describe another relevant component]
- **Happy Path Flow:**
  1. [Describe the first user interaction step for '{{feature}}']
  2. [Describe the next step]
  3. ...
- **Business Rules / Validation:**
  - [Specify a rule relevant to '{{feature}}', e.g., Input validation, state change condition]
  - [Specify another rule]
- **Acceptance Criteria:**
  - GIVEN [Context] WHEN [Action related to '{{feature}}'] THEN [Expected Outcome]
{% endfor %}

## 4. Key Assumptions (from Clarifying Questions)
{% for q in clarifyingQuestions %}
- **Q:** {{q.question}}  
  **A:** {{q.suggestedAnswer}} _(Dimension: {{q.dimension}})_
{% endfor %}

## 5. Non-Functional Requirements
- Performance  
- Security  
- Accessibility  
- Error handling

## 6. Companion Specs
| Document         | Purpose                  |
|------------------|--------------------------|
| \`userflow.md\`    | Screen-to-screen UX      |
| \`architecture.md\`| Stack & data flow        |
| \`api_spec.md\`    | Endpoint contracts       |
| \`data_model.md\`  | DB tables and relations  |

## 7. Out-of-Scope (v1)
- Feature X  
- Feature Y

## 8. Open Questions / Future Work
- [Needs Clarification: e.g., integration method with Vercel v0]`
};
