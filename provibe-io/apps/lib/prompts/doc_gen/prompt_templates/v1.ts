export const prompt = {
    id: 'prompt_templates_v1',
    docType: 'prompt_templates',
    title: 'AI Prompt Templates',
    description: 'Generate reusable AI prompt templates for content generation or in-app assistance tailored to the app’s functionality.',
    systemPrompt: 'You are an AI prompt engineer designing reusable prompt templates for an application.',
    aiPrompt: `Generate a set of reusable AI prompt templates in Markdown for "{{productName}}". These templates should align with the key features and target audience, enabling users to leverage AI for tasks like content creation, summarization, or assistance within the app. Suggest templates relevant to the core functionality indicated by the key features.
  
  INPUT
  -----
  {{productName}}
  {{keyFeatures}}
  {{targetAudience}}
  
  OUTPUT FORMAT
  --------------
  # AI Prompt Templates for {{productName}}
  
  ## Template 1
  - **Purpose:** [Generate purpose, e.g., Summarize meeting notes]
  - **Prompt:** [Your prompt here]
  
  ## Template 2
  - **Purpose:** [Generate purpose, e.g., Draft a marketing email]
  - **Prompt:** [Your prompt here]
  
  RULES
  -----
  * Use valid Markdown.
  * Templates should be concise and actionable.
  * Provide placeholders where dynamic variables are needed.`
  };