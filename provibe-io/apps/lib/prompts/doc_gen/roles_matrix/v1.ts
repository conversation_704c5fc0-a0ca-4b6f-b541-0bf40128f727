export const prompt = {
  id: 'roles_matrix_v1',
  docType: 'roles_matrix',
  title: 'User Roles & Permissions Matrix',
  description: 'Outline the access control levels for different types of users and the permissions associated with each role.',
  systemPrompt: 'You are a product architect defining user roles and permissions.',
  aiPrompt: `Generate a Roles & Permissions matrix in Markdown for "{{productName}}". Use the provided roles and map them to the key application areas/permissions. Also, provide a brief description for each role.

INPUT
-----
{{productName}}
{{roles}} ← array of role names
{{permissions}} ← list of key app areas/actions (features or modules)

OUTPUT FORMAT
--------------
# Roles & Permissions – {{productName}}

## Role Matrix
| Role | {{permissions.join(' | ')}} |
|------|------------------------------|
{% for role in roles %} {# Assume '✓' indicates access. More complex logic needed for R/W/Admin #}
| {{role}} | {% for p in permissions %} ✓ {% endfor %} |
{% endfor %}

## Role Descriptions
{% for role in roles %}
### {{role}}
- Description of the role's purpose
- Key Access Areas: [List relevant permissions from the matrix]
{% endfor %}

RULES
-----
* Use Markdown tables
* Show clear access mapping for each role`
};
