export const prompt = {
    id: 'sample_data_v1',
    docType: 'sample_data',
    title: 'Sample Data for Testing',
    description: 'Create realistic sample data entries for core entities in the application to be used during development or testing.',
    systemPrompt: 'You are a QA engineer generating realistic sample data for application testing.',
    aiPrompt: `Generate sample data in Markdown format for the key entities of "{{productName}}".
For each entity listed in \`keyEntities\`, create a table with relevant columns (inferring common fields like id, name, description, timestamps, and foreign keys based on the entity type) and populate it with 3-5 rows of realistic, fictional data.
  
  INPUT
  -----
  {{productName}}
  {{keyEntities}} ← list of key entities (e.g., users, projects, documents)
  {{keyFeatures}} (*Optional: To help infer relevant fields*)
  
  OUTPUT FORMAT
  --------------
  # Sample Data for {{productName}}
  
  {% for entity in keyEntities %}
  ## Sample {{entity}} Data
  | id (UUID) | name (text) | created_at (timestamp) | [Other relevant fields...] |
  |-----------|-------------|------------------------|----------------------------|
  | [UUID]    | [Sample Name 1] | [Timestamp]            | [...]                      |
  | [UUID]    | [Sample Name 2] | [Timestamp]            | [...]                      |
  | [UUID]    | [Sample Name 3] | [Timestamp]            | [...]                      |
  {% endfor %}
  
  RULES
  -----
  * Use Markdown for formatting.
  * Ensure data is realistic and varied.
  * Do not include actual sensitive information.`
  };