export const prompt = {
  id: 'test_plan_v1',
  docType: 'test_plan',
  title: 'Test Plan & Cases',
  description: 'Develop a basic test plan with functional and edge-case scenarios to validate the core features.',
  systemPrompt: 'You are a QA lead creating a functional test plan.',
  aiPrompt: `Generate a functional test plan in Markdown for "{{productName}}".
For each key feature, define relevant user stories, test cases (including inputs, expected outcomes), and specific edge cases to consider. Include a mix of positive and negative test scenarios.

INPUT
-----
{{productName}}
{{keyFeatures}} ← list of feature names
{{targetAudience}}

OUTPUT FORMAT
--------------
# Test Plan for {{productName}}

## Overview
Brief test objectives and scope

## Feature-wise Test Cases
{% for feature in keyFeatures %}
### {{feature}}
- **User Story:** As a {{targetAudience}}, I want ...
- **Test Case 1 (Positive):** [Describe test]
  - **Input:** ...
  - **Expected Outcome:** ...
- **Test Case 2 (Negative/Edge):** [Describe test]
  - **Input:** ...
  - **Expected Outcome:** ...
- **Other Edge Cases:** [List potential edge conditions, e.g., invalid inputs, empty states, boundary values]
{% endfor %}

## Summary
- Number of scenarios
- Coverage gaps (if any)

RULES
-----
* Use clean Markdown format
* Keep each test concise and useful
* Focus on real-world usage flows`
};
