export const prompt = {
  id: 'ui_flows_v1',
  docType: 'ui_flows',
  title: 'UI Flows',
  description: 'Map out screen-to-screen navigation and user interactions across the app’s interface.',
  systemPrompt: 'You are a UX engineer describing UI flows and screen transitions.',
  aiPrompt: `Generate UI flow descriptions in Markdown for "{{productName}}".
Detail the purpose, entry points, key components, and transitions for each screen provided in the \`screens\` input.

INPUT
-----
{{productName}}
{{keyFeatures}} ← list of key features (for context)
{{screens}} ← list of core screen objects, each structured like: { name: string, purpose: string, entryPoints: string[], components: string[], transitions: [{ trigger: string, targetScreen: string }] }

OUTPUT FORMAT
--------------
# UI Flows for {{productName}}

## Screens & Flows
{% for screen in screens %}
### {{screen.name}}
- **Purpose:** {{screen.purpose}}
- **Entry Points:** {{screen.entryPoints}}
- **Components:** {{screen.components}}
- **Transitions:** 
  {% for transition in screen.transitions %}
  - On {{transition.trigger}} → go to {{transition.targetScreen}}
  {% endfor %}
{% endfor %}

## Common UI Patterns
- Navigation bar
- Modal dialogs
- Form interactions
- Feedback messages

RULES
-----
* Keep transitions clear and interface-driven
* Use headings and bullets for each screen
* Don’t include code or diagrams`
};
