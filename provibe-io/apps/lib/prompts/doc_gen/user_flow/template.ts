export const userFlowTemplate = {
  id: 'user_flow_template_v1',
  title: 'User Flow – {{productName}}',
  markdown: `# User Flow – {{productName}}  
*Version 1.0 | {{currentDate}}*

## 1. Entry Point  
Describe how the user first interacts with the app (e.g., landing page, login/signup, redirect from external platform).

## 2. Onboarding  
Explain the initial setup or personalization flow — tool selection, goal definition, permission grants, etc.

## 3. Primary Navigation Flow  
For each numbered feature in \`keyFeatures\`, describe:
- How the user reaches it  
- The core interaction flow  
- What triggers next

## 4. Completion Flow  
What occurs when a user finishes their core task (e.g., document generation, export, preview, confirmation)?

## 5. Loopbacks and Exit Paths  
Describe optional or supporting paths — editing previous steps, logging out, accessing history, canceling actions.`
};

