export const prompt = {
  id: 'user_flow_v1',
  docType: 'user_flow',
  title: 'User Flow',
  description: 'Create a detailed description of how users will interact with the application, from entry points to final outcomes.',
  systemPrompt: 'You are a senior UX strategist describing the primary user journeys through an application, suitable for guiding development by humans or AI agents.',
  aiPrompt: `Generate a User Flow document in Markdown for "{{productName}}".
Follow the structure defined below precisely. Describe the step-by-step journey a typical user takes, covering entry points, onboarding (if applicable), primary feature interactions, completion flows, and common loopbacks or exit paths. Focus on user actions and system responses/transitions.

Use the inputs below to populate each section of the defined output format. Write in short, complete sentences. Describe behavior and transitions clearly.

---
INPUT VARS
-----------
{{productName}}  
{{refinedIdea}}  
{{targetAudience}}  
{{usp}}  
{{keyFeatures}}            ← List of feature names. Properly number them  
{{clarifyingQuestions}}    ← Array of {question, suggestedAnswer, dimension}  
{{currentDate}}

---

💡 OUTPUT FORMAT:

# User Flow – {{productName}}  
*Version 1.0 | {{currentDate}}*

## 1. Entry Point  
Describe how the user first interacts with the app (e.g., landing page, login/signup, redirect from external platform).

## 2. Onboarding  
Explain the initial setup or personalization flow if applicable (e.g., tool selection, goal definition, permission grants). If no specific onboarding, state that.

## 3. Primary Navigation Flow  
{% for feature in keyFeatures %}
### {{loop.index}}. {{feature}} Flow
- **Entry:** Describe how the user typically navigates to start interacting with '{{feature}}'.
- **Interaction:** Detail the core steps the user takes to use '{{feature}}'.
- **Next Step/Trigger:** Explain what happens upon completion or the primary action within this flow (e.g., navigates to results page, data is saved, modal closes).
{% endfor %}

## 4. Completion Flow  
What occurs when a user finishes their core task (e.g., document generation, export, preview, confirmation)?

## 5. Loopbacks and Exit Paths  
Describe optional or supporting paths — editing previous steps, logging out, accessing history, canceling actions.

---

✅ RULES:
- Output valid Markdown. Do not use code blocks for the flow description.
- Do not include intros, summaries, or commentary
- Focus on interface behaviors and clear step transitions
- Ensure the description is clear and actionable enough for a developer or AI agent to implement the flow.
`
};
