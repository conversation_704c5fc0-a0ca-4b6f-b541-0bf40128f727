export const prompt = {
  id: 'wireframe_v1',
  docType: 'wireframe',
  title: 'Wireframe Mockups',
  description: 'Describe and sketch basic UI wireframes for each core screen of the application based on the user flows and feature list.',
  systemPrompt: 'You are a product designer describing wireframe layouts for key application screens.',
  aiPrompt: `Generate Markdown descriptions of wireframes for the core screens of "{{productName}}".
For each screen provided, describe its purpose, high-level layout (Header, Main, Footer), and key interactive elements like buttons, inputs, lists, or cards.

INPUT
-----
{{productName}}
{{keyFeatures}} (*For context*)
{{screens}} ← list of screen objects, each like: { name: string, purpose: string, buttonLabels?: string[], inputFields?: string[] }

OUTPUT FORMAT
--------------
# Wireframes for {{productName}}

{% for screen in screens %}
## {{screen.name}}
- **Purpose:** {{screen.purpose}}
- **Layout:**
  - Header: [Title / Nav / CTA]
  - Main Area: Key content blocks or components
  - Footer: Navigation or status
- **Elements:**
  - Buttons: {{screen.buttonLabels | default('Describe key CTAs')}}
  - Input Fields: {{screen.inputFields | default('Describe necessary inputs')}}
  - Lists / Cards / Icons: Describe visual structure
{% endfor %}

RULES
-----
* Use Markdown
* Describe layouts and visual grouping
* Do not include actual drawings or diagrams`
};
