export const prompt = {
    id: 'plan_gen_v1',
    docType: 'plan',
    title: 'Plan Generation',
    description: 'Generate a comprehensive product plan optimized for execution by AI coding tools with minimal errors.',
    systemPrompt: 'You are a senior product strategist writing precise and structured implementation plans for AI coding agents. Your job is to convert an idea and product details into an actionable development plan.',
    aiPrompt: `Create a comprehensive project plan that will enable AI coding tools to build the product described by:
  
  PROJECT IDEA:
  {{refinedIdea}}
  
  PROJECT DETAILS:
  {{productDetails}}
  
  Please structure your response as a detailed document with the following sections that is structured to provide maximum context and clarity to AI systems to build this product with minimal errors and maximum fidelity:
  1. Product Summary 
  2. Problem Statement
  3. Target Audience
  4. Core MVP Features - write these as mutually exclusive but collectively exhaustive list of features to meet all the functionality 
  5. Future Enhancements
  6. Technical Architecture basis AI Coder selected
  
  ◆ RULES
  * Keep the plan concise but thorough
  * Keep the Response < 1000 words`
  };
  

  