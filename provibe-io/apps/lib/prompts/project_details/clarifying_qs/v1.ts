

// /lib/prompts/project_details/clarifying_qs/v1.ts

export const clarifyingQsPromptV1 = `You are assisting in structuring a user's product idea. Given the refined concept:

1. Validate the idea (if invalid, provide reason).

2. If valid, generate structured product details including:
   - **Target Audience**: Clearly define ideal user groups.
   - **Problem Solved**: Clearly articulate the problems solved for user groups.
   - **Key Features**: Suggest 3-5 essential features. Mutually exclusive but collectively exhaustive to meet all the functionality
   - **Frontend Tech Stack**: Suggest suitable front-end technologies to meet all defined features.
   - **Backend Tech Stack**: Suggest suitable backend technologies to meet all defined features.
   - **Unique Selling Points (USP)**: Highlight 2-3 distinguishing factors.

3. Additionally, create 3-5 thoughtful clarifying questions that help refine the product's direction, technical details, user interactions, or business model. Provide insightful suggested answers aligning with industry best practices.

OUTPUT must be JSON without markdown formatting:
{
  "isValid": true/false,
  "reason": "<reason if invalid>",
  "productDetails": {
    "targetAudience": "<string>",
    "problemSolved" : "<string>",
    "keyFeatures": ["<feature1>", "<feature2>", "..."],
    "frontendTech": ["<tech1>", "<tech2>", "..."],
    "backendTech": ["<tech1>", "<tech2>", "..."],
    "usp": ["<usp1>", "<usp2>", "..."]
  },
  "clarifyingQuestions": [
    {
      "question": "<string>",
      "suggestedAnswer": "<string>",
      "dimension": "<product|technical|user|business>"
    }
  ]
}

Refined idea:
{{refined_idea}}

RAW_IDEA:
---
{{RAW_IDEA}}
---

OPTIONAL_HINTS:
{{OPTIONAL_HINTS}}`;