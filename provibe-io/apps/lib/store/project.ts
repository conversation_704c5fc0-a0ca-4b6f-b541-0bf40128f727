import { create } from 'zustand'
import { supabase } from '@/lib/supabase-client' // Assuming supabase client is here
// TODO: Import `useToast` if `handleError` uses it directly
// import { useToast } from "@/components/ui/use-toast";

// --- Types ---
// (Keep the type definitions as they were)
export type ClarifyingQuestion = {
  question: string;
  suggestedAnswer: string;
  userAnswer?: string;
  dimension?: string;
  isEditing?: boolean; // Optional: for UI state
  isDeleted?: boolean; // Optional: for UI state
}

export type ProductDetails = {
  targetAudience?: string; // Made optional if sometimes empty
  keyFeatures?: string[];
  frontendTech?: string[];
  backendTech?: string[];
  usp?: string[];
}

// Combined Project State Type
export type ProjectState = {
  // Project metadata
  projectId: string | null;
  userId: string | null; // Added userId based on previous context
  projectName: string;
  createdAt: Date | null;
  updatedAt: Date | null;
  status: 'draft' | 'active' | 'completed'; // Added 'active'

  // Project content
  idea: string;
  refinedIdea: string;
  voiceNoteUrl: string | null; // Added voiceNoteUrl
  selectedTools: string[];
  productDetails: ProductDetails | null; // Allow null initial state
  projectPlan: string | null; // Allow null initial state
  clarifyingQuestions: ClarifyingQuestion[];

  // UI State (optional - could be local component state)
  // currentStep: number; // Might be better as local state in the page component
  isTestUser: boolean;
  isLoading: boolean; // General loading state for store actions
  isRefining: boolean; // Specific loading for refining idea
  isGeneratingQuestions: boolean; // Specific loading
  isGeneratingPlan: boolean; // Specific loading
  isEditMode: boolean;

  // --- Actions ---

  // Core Setters (Essential for loading data)
  setProjectId: (id: string | null) => void;
  setUserId: (id: string | null) => void;
  setProjectName: (name: string) => void;
  setCreatedAt: (date: Date | null) => void;
  setUpdatedAt: (date: Date | null) => void;
  setStatus: (status: 'draft' | 'active' | 'completed') => void;
  setIdea: (idea: string) => void;
  setRefinedIdea: (idea: string) => void;
  setVoiceNoteUrl: (url: string | null) => void;
  setSelectedTools: (tools: string[]) => void;
  setProductDetails: (details: ProductDetails | null) => void;
  setProjectPlan: (plan: string | null) => void;
  setClarifyingQuestions: (questions: ClarifyingQuestion[]) => void;
  setIsTestUser: (isTest: boolean) => void;
  setEditMode: (isEditing: boolean) => void;
  reset: () => void; // Action to reset the store state

  // Async Actions (Interact with backend/APIs)
  // initializeProject: (idea: string, userId: string) => Promise<string | null>; // Moved out - handled by Dashboard/creation flow
  updateProject: () => Promise<void>; // Main save action
  markProjectAsCompleted: (projectId: string) => Promise<void>; // Takes ID now
  refineIdea: (currentIdea: string) => Promise<string | null>; // Returns enhanced idea or null on error
  generateClarifyingQuestions: (idea: string, refinedIdea: string) => Promise<void>;
  generateProjectPlan: () => Promise<void>; // Placeholder
  createNewProject: (userId: string, idea?: string) => Promise<string | null>; // Modified: idea is optional
}

// Initial State Definition
const initialState: Omit<ProjectState, keyof ReturnType<typeof createProjectActions>> = {
    projectId: null,
    userId: null,
    projectName: 'Untitled Project',
    createdAt: null,
    updatedAt: null,
    status: 'draft' as 'draft' | 'active' | 'completed',
    idea: '',
    refinedIdea: '',
    voiceNoteUrl: null,
    selectedTools: [],
    productDetails: null,
    projectPlan: null,
    clarifyingQuestions: [],
    isTestUser: false,
    isLoading: false,
    isRefining: false,
    isGeneratingQuestions: false,
    isGeneratingPlan: false,
    isEditMode: false,
};

// Separate actions for better organization
const createProjectActions = (set: any, get: any) => ({
  // --- Simple Setters ---
  setProjectId: (id) => set({ projectId: id }),
  setUserId: (id) => set({ userId: id }),
  setProjectName: (name) => set({ projectName: name }),
  setCreatedAt: (date) => set({ createdAt: date }),
  setUpdatedAt: (date) => set({ updatedAt: date }),
  setStatus: (status) => set({ status: status }),
  setIdea: (idea) => set({ idea }),
  setRefinedIdea: (idea) => set({ refinedIdea: idea }),
  setVoiceNoteUrl: (url) => set({ voiceNoteUrl: url }),
  setSelectedTools: (tools) => set({ selectedTools: tools }),
  setProductDetails: (details) => set({ productDetails: details }),
  setProjectPlan: (plan) => set({ projectPlan: plan }),
  setClarifyingQuestions: (questions) => set({ clarifyingQuestions: questions }),
  setIsTestUser: (isTest) => set({ isTestUser: isTest }),
  setEditMode: (isEditing) => set({ isEditMode: isEditing }),
  reset: () => {
    console.log("Resetting project store to initial state");
    // Instead of resetting everything, keep the projectId and userId
    const { projectId, userId } = get();
    set({
      ...initialState,
      projectId,
      userId
    });
  },

  // Add a reset function to clear all project state
  resetProject: () => {
    set({
      projectId: null,
      projectName: 'Untitled Project',
      idea: '',
      refinedIdea: null,
      productDetails: { targetAudience: '', problemSolved: '', keyFeatures: '', successMetrics: '', timeline: '' },
      selectedTools: [],
      projectPlan: null,
      clarifyingQuestions: [],
      voiceNoteUrl: null,
      status: 'idle',
      createdAt: null,
      updatedAt: null,
      // Keep any user-related state intact
    });
    console.log("Project store has been reset");
  },

  // --- Async Actions ---

  updateProject: async (partialProject = {}) => {
    const state = get();
    const { projectId, isTestUser } = state;
    if (isTestUser || !projectId) {
      console.log("Update skipped: Test user or no Project ID.");
      return;
    }

    set({ isLoading: true });
    try {
      const now = new Date();
      
      // First, get the current project data from the database
      const { data: currentProject, error: fetchError } = await supabase
        .from("projects")
        .select("*")
        .eq("id", projectId)
        .single();
      
      if (fetchError) {
        console.error("Error fetching current project data:", fetchError);
        throw fetchError;
      }
      
      // Only update the fields provided in partialProject
      const updateData = {
        ...partialProject,
        updated_at: now.toISOString()
      };
      
      // Preserve product_details if it's not in the update
      if (!('product_details' in partialProject) && currentProject.product_details) {
        console.log("Preserving existing product_details");
        updateData.product_details = currentProject.product_details;
      }
      
      // Preserve clarifying_questions if it's not in the update
      if (!('clarifying_questions' in partialProject) && currentProject.clarifying_questions) {
        console.log("Preserving existing clarifying_questions");
        updateData.clarifying_questions = currentProject.clarifying_questions;
      }
      
      // Preserve individual fields if they're not in the update
      const fieldsToPreserve = ['tg', 'features', 'usp', 'problems', 'frontendTech', 'backendTech'];
      fieldsToPreserve.forEach(field => {
        if (!partialProject[field] && currentProject[field] !== undefined) {
          console.log(`Preserving existing ${field}`);
          updateData[field] = currentProject[field];
        }
      });
      
      console.log("Updating project with:", updateData);
      
      const { error } = await supabase
        .from("projects")
        .update(updateData)
        .eq("id", projectId);
      
      if (error) throw error;
      
      // Update the store with the partial project
      set(state => ({
        ...state,
        ...partialProject,
        updatedAt: now
      }));
      
      set({ isLoading: false });
      return { success: true };
    } catch (error) {
      console.error("Error updating project:", error);
      set({ isLoading: false });
      return { success: false, error };
    }
  },

  markProjectAsCompleted: async (projectId: string) => {
    const { isTestUser } = get();
    if (isTestUser || !projectId) {
       console.log("Mark as complete skipped: Test user or no Project ID.");
       return;
    }

    set({ isLoading: true });
    try {
      const now = new Date();
      const { error } = await supabase
        .from('projects')
        .update({
          status: 'completed',
          updated_at: now.toISOString()
        })
        .eq('id', projectId);

      if (error) {
         console.error('Supabase complete error:', error);
         throw new Error(`Failed to mark project as completed: ${error.message}`);
      }
      set({ status: 'completed', updatedAt: now, isLoading: false });
      console.log("Project marked as completed.");
    } catch (error) {
      console.error('Error in markProjectAsCompleted action:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  refineIdea: async (currentIdea) => {
    const { isTestUser, projectId, updateProject } = get(); // Get updateProject action
    set({ isRefining: true });
    let enhancedIdea = null;
    let productName = null;
    
    try {
      // Ensure API route exists and is configured
      const response = await fetch('/api/enhance-idea', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ idea: currentIdea }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API Error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      enhancedIdea = data.enhancedIdea; // Store the enhanced idea
      productName = data.productName; // Store the product name

      if (!enhancedIdea) {
        throw new Error("API returned empty enhanced idea.");
      }

      // Update the store with both the enhanced idea and product name
      set({ 
        refinedIdea: enhancedIdea,
        projectName: productName || get().projectName // Use existing name if none returned
      });

      // If we have a project ID and not a test user, update the database
      if (projectId && !isTestUser) {
        const updateData: any = { refined_idea: enhancedIdea };
        
        // Only update name if we got one from the API
        if (productName) {
          updateData.name = productName;
        }
        
        await updateProject(updateData);
      }

      return { enhancedIdea, productName };
    } catch (error) {
      console.error("Error refining idea:", error);
      throw error;
    } finally {
      set({ isRefining: false });
    }
  },

  generateClarifyingQuestions: async (idea) => {
    const { isTestUser, projectId } = get();
    set({ isGeneratingQuestions: true });
    try {
      const response = await fetch('/api/generate-clarifying-questions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          idea,
          enhancedIdea: get().refinedIdea || idea,
          projectId // Pass the projectId to the API
        }),
      });

      if (!response.ok) throw new Error('Failed to generate questions');

      const data = await response.json(); // Expecting { clarifyingQuestions: [], productDetails: {} }

      const formattedQuestions = (data.clarifyingQuestions || []).map((q: any) => ({
        question: q.question || "Unnamed Question",
        suggestedAnswer: q.suggestedAnswer || "",
        userAnswer: q.suggestedAnswer || "", // Default user answer to suggested
        dimension: q.dimension || "General"
      }));

      const productDetails = data.productDetails || {};

      // Update state
      set({
        clarifyingQuestions: formattedQuestions,
        productDetails: productDetails,
        isGeneratingQuestions: false
       });

      // Update database
      if (!isTestUser && projectId) {
        try {
            await updateProject(); // Use the main update action
        } catch (dbError) {
            console.error("DB update failed after generating questions:", dbError);
            // Optionally revert state
            // set({ clarifyingQuestions: get().clarifyingQuestions, productDetails: get().productDetails });
            throw dbError;
        }
      }
    } catch (error) {
      console.error('Error generating questions:', error);
      set({ isGeneratingQuestions: false });
      throw error;
    }
  },

  generateProjectPlan: async () => {
    const { projectId, isTestUser, idea, refinedIdea, clarifyingQuestions, selectedTools, productDetails, updateProject } = get();
     if (!projectId || isTestUser) {
         console.log("Plan generation skipped.");
         return;
     };

    set({ isGeneratingPlan: true });
    try {
        // --- Prepare data for API ---
        const questionsText = clarifyingQuestions.length > 0
            ? clarifyingQuestions.map(q => `Q: ${q.question}\nA: ${q.userAnswer || q.suggestedAnswer || 'N/A'}`).join('\n\n')
            : 'No clarifying questions answered.';

        const toolsText = selectedTools.length > 0 ? selectedTools.join(', ') : 'No specific tools selected.';
        const detailsText = productDetails ? JSON.stringify(productDetails, null, 2) : 'No product details provided.';

        // --- Call API --- (Ensure '/api/gemini-plan' exists and accepts this format)
         const response = await fetch('/api/gemini-plan', { // Replace with your actual plan generation API endpoint
             method: 'POST',
             headers: { 'Content-Type': 'application/json' },
             body: JSON.stringify({
                 idea: idea,
                 refinedIdea: refinedIdea,
                 questionsText: questionsText,
                 toolsText: toolsText,
                 detailsText: detailsText,
                 // Add any other necessary fields like projectId or user info if API requires
             }),
         });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Plan Generation API Error (${response.status}): ${errorText}`);
        }

        const data = await response.json();
        const plan = data.plan; // Adjust based on actual API response structure

         if (!plan) {
             throw new Error("API returned empty project plan.");
         }

        // --- Update State & DB ---
         set({ projectPlan: plan, isGeneratingPlan: false });

        try {
             await updateProject(); // Save the new plan to DB
         } catch (dbError) {
             console.error("DB update failed after generating plan:", dbError);
             // Optionally revert state
             // set({ projectPlan: get().projectPlan });
             throw dbError;
         }

    } catch (error) {
        console.error('Error generating project plan:', error);
        set({ isGeneratingPlan: false });
        throw error;
    }
  },

  // *** This is the action used by the "New Project" button ***
  createNewProject: async (userId: string, idea?: string): Promise<string | null> => {
    if (!userId) {
        console.error("Cannot create project without user ID.");
        throw new Error("User not authenticated.");
    }
    // Removed validation for idea length here, as it's optional

    set({ isLoading: true });
    const trimmedIdea = idea?.trim(); // Trim if idea exists

    try {
        const now = new Date();
        const insertData: any = { // Use 'any' or define a specific insert type
            user_id: userId,
            name: 'Untitled Project', // Default name
            status: 'draft',
            created_at: now.toISOString(),
            updated_at: now.toISOString()
        };

        // Only include idea and refined_idea if an idea was provided
        if (trimmedIdea && trimmedIdea.length > 0) {
            insertData.idea = trimmedIdea;
            insertData.refined_idea = trimmedIdea; // Initial refined idea is the same
        } else {
           // *** This handles the case where no idea is passed ***
           // Sets idea and refined_idea to empty strings in the database
           insertData.idea = "";
           insertData.refined_idea = "";
        }


        const { data, error } = await supabase
            .from('projects')
            .insert(insertData)
            .select('id') // Select only the ID
            .single(); // Expect a single row back

        if (error) {
            console.error('Supabase insert error:', error);
            throw new Error(`Failed to create project: ${error.message}`);
        }

        if (!data || !data.id) {
             throw new Error("Project created but failed to retrieve ID.");
        }

        console.log(`New project created with ID: ${data.id}`);
        set({ isLoading: false });
        return data.id; // Return the new project ID

    } catch (error) {
        console.error('Error in createNewProject action:', error);
        set({ isLoading: false });
        throw error; // Re-throw the error for the calling component
    }
  }

});

// Create the Zustand store
export const useProjectStore = create<ProjectState>((set, get) => ({
  ...initialState,
  ...createProjectActions(set, get),
}));

// --- Optional: Helper Functions (Export separately or integrate if needed) ---

// Example: If you need a shared error handler outside the store hook
// export const handleError = (error: any, toast: any) => { // Pass toast dependency
//   console.error("Store Error:", error);
//   if (toast) {
//     toast({
//       title: "Error",
//       description: error.message || "An unexpected error occurred",
//       variant: "destructive"
//     });
//   }
// }

// Make sure the createNewProject function is exported
export const createNewProject = async (userId: string, idea?: string): Promise<string | null> => {
  if (!userId) {
    console.error("Cannot create project without user ID.");
    throw new Error("User not authenticated.");
  }
  
  try {
    const now = new Date();
    const insertData: any = {
      user_id: userId,
      name: 'Untitled Project', // Default name
      status: 'draft',
      created_at: now.toISOString(),
      updated_at: now.toISOString()
    };

    // Only include idea and refined_idea if an idea was provided
    if (idea && idea.trim().length > 0) {
      insertData.idea = idea.trim();
      insertData.refined_idea = idea.trim(); // Initial refined idea is the same
    } else {
      // This handles the case where no idea is passed
      insertData.idea = "";
      insertData.refined_idea = "";
    }

    // Insert the new project
    const { data, error } = await supabase
      .from('projects')
      .insert(insertData)
      .select('id')
      .single();

    if (error) {
      console.error("Error creating new project:", error);
      throw new Error(`Failed to create project: ${error.message}`);
    }

    if (!data?.id) {
      throw new Error("No project ID returned from database");
    }

    console.log(`Project created successfully with ID: ${data.id}`);
    return data.id;
  } catch (error) {
    console.error("Error in createNewProject:", error);
    throw error; // Re-throw to allow caller to handle
  }
};
