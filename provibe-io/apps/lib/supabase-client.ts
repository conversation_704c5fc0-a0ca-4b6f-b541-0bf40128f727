import { createClient } from "@supabase/supabase-js"
import type { Database } from "@/types/supabase"

// Use let instead of const to allow conditional assignment
let supabaseInstance: ReturnType<typeof createClient<Database>> | null = null

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables")
}

// Function to get domain-specific cookie configuration
const getCookieConfig = () => {
  // Determine the deployment environment and domain
  const isProduction = process.env.NODE_ENV === 'production'

  // Try multiple ways to determine the current domain
  let currentDomain = ''

  // 1. Check explicit environment variable (recommended) - PRIORITIZE THIS
  if (process.env.NEXT_PUBLIC_APP_DOMAIN) {
    currentDomain = process.env.NEXT_PUBLIC_APP_DOMAIN
    console.log("Using explicit domain from NEXT_PUBLIC_APP_DOMAIN:", currentDomain)
  }
  // 2. Runtime detection (client-side only) - SECOND PRIORITY
  else if (typeof window !== 'undefined') {
    currentDomain = window.location.hostname
    console.log("Using runtime detected domain:", currentDomain)
  }
  // 3. Check Vercel URL (automatic in Vercel deployments) - LAST RESORT
  else if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    currentDomain = process.env.NEXT_PUBLIC_VERCEL_URL
    console.log("Using Vercel public URL:", currentDomain)
  }
  // 4. Check Vercel branch URL - FALLBACK
  else if (process.env.VERCEL_URL) {
    currentDomain = process.env.VERCEL_URL
    console.log("Using Vercel internal URL:", currentDomain)
  }

  // Default configuration
  let cookieName = "sb-provibe-default-auth-token"
  let domain = ""

  // Configure based on detected domain
  if (currentDomain) {
    if (currentDomain.includes('app.provibe.io')) {
      // Configuration for app.provibe.io deployment
      cookieName = "sb-provibe-app-auth-token"
      domain = isProduction ? "app.provibe.io" : ""
    } else if (currentDomain.includes('www.provibe.dev') || currentDomain.includes('provibe.dev')) {
      // Configuration for www.provibe.dev deployment
      cookieName = "sb-provibe-main-auth-token"
      domain = isProduction ? "www.provibe.dev" : ""
    } else if (currentDomain.includes('localhost') || currentDomain.includes('127.0.0.1')) {
      // Local development
      cookieName = "sb-provibe-dev-auth-token"
      domain = ""
    } else {
      // Unknown domain - use domain-specific cookie name
      const domainSafe = currentDomain.replace(/[^a-zA-Z0-9]/g, '-')
      cookieName = `sb-provibe-${domainSafe}-auth-token`
      domain = isProduction ? currentDomain : ""
    }
  } else {
    // Fallback if no domain detected - use runtime detection
    console.warn("No domain detected in environment, attempting runtime detection")
    if (typeof window !== 'undefined') {
      const runtimeDomain = window.location.hostname
      console.log("Runtime detected domain:", runtimeDomain)

      if (runtimeDomain.includes('app.provibe.io')) {
        cookieName = "sb-provibe-app-auth-token"
        domain = isProduction ? "app.provibe.io" : ""
      } else if (runtimeDomain.includes('www.provibe.dev') || runtimeDomain.includes('provibe.dev')) {
        cookieName = "sb-provibe-main-auth-token"
        domain = isProduction ? "www.provibe.dev" : ""
      } else if (runtimeDomain.includes('localhost') || runtimeDomain.includes('127.0.0.1')) {
        cookieName = "sb-provibe-dev-auth-token"
        domain = ""
      } else {
        const domainSafe = runtimeDomain.replace(/[^a-zA-Z0-9]/g, '-')
        cookieName = `sb-provibe-${domainSafe}-auth-token`
        domain = isProduction ? runtimeDomain : ""
      }
    } else {
      // Ultimate fallback for server-side rendering
      cookieName = "sb-provibe-fallback-auth-token"
      domain = ""
    }
  }

  return {
    name: cookieName,
    lifetime: 60 * 60 * 8, // 8 hours
    domain: domain,
    path: "/",
    sameSite: "lax" as const,
    secure: isProduction, // Only use secure cookies in production
  }
}

// Create a singleton instance of the Supabase client
export const supabase = (() => {
  if (supabaseInstance) return supabaseInstance

  const cookieConfig = getCookieConfig()

  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: "pkce", // Use PKCE flow for better security
      storageKey: `supabase.auth.token.${cookieConfig.name}`, // Use domain-specific storage key
    },
  })

  return supabaseInstance
})()

// Export a function to get the client for cases where you need a fresh reference
export const getSupabaseClient = () => supabase

// Export the cookie configuration for use in other parts of the app
export const getSupabaseCookieConfig = getCookieConfig

// Export a function to check if we're in a specific deployment environment
export const getDeploymentInfo = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const cookieConfig = getCookieConfig()

  // Determine current domain using the same logic as getCookieConfig
  let currentDomain = ''
  if (process.env.NEXT_PUBLIC_APP_DOMAIN) {
    currentDomain = process.env.NEXT_PUBLIC_APP_DOMAIN
  } else if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    currentDomain = process.env.NEXT_PUBLIC_VERCEL_URL
  } else if (process.env.VERCEL_URL) {
    currentDomain = process.env.VERCEL_URL
  } else if (typeof window !== 'undefined') {
    currentDomain = window.location.hostname
  }

  return {
    isProduction,
    currentDomain,
    detectionMethod: process.env.NEXT_PUBLIC_APP_DOMAIN ? 'explicit' :
                    process.env.NEXT_PUBLIC_VERCEL_URL ? 'vercel_public' :
                    process.env.VERCEL_URL ? 'vercel_internal' :
                    typeof window !== 'undefined' ? 'runtime' : 'unknown',
    isMainApp: currentDomain.includes('www.provibe.dev') || currentDomain.includes('provibe.dev'),
    isAppDomain: currentDomain.includes('app.provibe.io'),
    isLocalDev: currentDomain.includes('localhost') || currentDomain.includes('127.0.0.1'),
    cookieConfig
  }
}
