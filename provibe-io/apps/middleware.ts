import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Remove any Clerk imports like:
// import { authMiddleware } from '@clerk/nextjs';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  
  // Skip middleware for static assets and auth routes for performance
  const { pathname } = req.nextUrl;
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.startsWith('/auth/') ||
    pathname.includes('.') // Skip files (css, js, images, etc.)
  ) {
    return res;
  }
  
  // Only run session check for protected dashboard routes
  if (pathname.startsWith('/dashboard')) {
    try {
      // Create a Supabase client for the middleware
      const supabase = createMiddlewareClient({ req, res });
      
      // This will refresh the session if needed and set the correct cookies
      const { data: { session } } = await supabase.auth.getSession();
      
      // Log the session status (in development only)
      if (process.env.NODE_ENV === 'development') {
        console.log('Middleware: Session exists:', !!session);
        if (session) {
          console.log('Middleware: User ID:', session.user.id);
        } else {
          console.log('Middleware: No session, checking cookies...');
        }
      }
      
      // If no session on protected route, redirect to login
      // But add exception for recent login attempts (check for auth cookies)
      if (!session) {
        const authCookies = req.cookies.get('sb-auth-token') || req.cookies.get('supabase-auth-token');
        if (authCookies) {
          console.log('Middleware: Found auth cookie, allowing access');
          return res; // Allow access if auth cookie exists
        }
        
        console.log('Middleware: No session or auth cookie, redirecting to login');
        return NextResponse.redirect(new URL('/auth/login', req.url));
      }
    } catch (error) {
      console.error('Middleware auth error:', error);
      // Continue without session check on error
    }
  }
  
  return res;
}

// Update the matcher to only run on dashboard routes for better performance
export const config = {
  matcher: [
    /*
     * Only match dashboard routes that need protection
     * This dramatically improves performance by avoiding middleware on:
     * - Static assets (_next/static, _next/image)
     * - API routes (api/*)
     * - Auth routes (auth/*)
     * - Public pages (/, favicon.ico, etc.)
     */
    '/dashboard/:path*',
  ],
};
