
Pro User Guide

| Task Type                   | Model Suggestion                         | Notes                                           |
|----------------------------|------------------------------------------|-------------------------------------------------|
| First-time PRD generation   | `gpt-4o`, `claude-3-sonnet`              | Best quality                                    |
| Doc manipulation (edits)    | `gpt-4o`, `claude-3-sonnet`              | Reliable rewriting and structured formatting    |
| Long input summarization    | `gpt-4o`, `claude-3-opus`                | High-accuracy compression with strong memory    |
| Idea → Outline              | `gpt-4o`, `claude-3-sonnet`              | Creative + structured breakdowns                |
| Chat clarifications on docs | `gpt-4o`, `claude-3-haiku`               | Fast and accurate replies to user questions     |


Free User Guide

| Task Type                   | Model Suggestion                                   | Notes                                           |
|----------------------------|----------------------------------------------------|-------------------------------------------------|
| First-time PRD generation   | `deepseek-chat`, `llama-3-70b-instruct`            | Decent structure, lower cost                   |
| Doc manipulation (edits)    | `qwen-1.5-72b`, `gemma-7b-it`, `llama-3-8b`         | Good for local rewrites and quick formatting   |
| Long input summarization    | `mixtral`, `qwen`                                  | Token-efficient and scalable for long inputs   |
| Idea → Outline              | `deepseek-chat`, `llama-3`                         | Reasonable creativity and structure            |
| Chat clarifications on docs | `gemma-7b-it`, `claude-3-haiku`                    | Fast response, low-latency lightweight models  |