provibe/
├── app/              # current /app directory
|   |-- (marketing)
|   |   ├──privacy/
|   |   ├──terms/
|   |   ├──layout.tsx
|   |   └──page.tsx
│   ├── _not-found.tsx
│   ├── error.tsx
│   ├── globals.css
│   ├── layout.tsx
│   ├── actions/
│   │   └── project-actions.ts
│   ├── api/
│   │   ├── analytics/
│   │   ├── enhance-idea/
│   │   ├── gemini-answer/
│   │   ├── generate-clarifying-questions/
│   │   └── transcribe/
│   └── styles/
│       └── tiptap.css
├── components/
│   ├── blocks/
│   │   └── editor-x/
│   │       ├── editor.tsx
│   │       ├── nodes.ts
│   │       └── plugins.tsx
│   ├── editor/
│   │   ├── context/
│   │   ├── editor-hooks/
│   │   ├── editor-ui/
│   │   ├── nodes/
│   │   ├── plugins/
│   │   ├── shared/
│   │   ├── themes/
│   │   ├── transformers/
│   │   └── utils/
│   ├── debug-panel.tsx
│   ├── document-viewer.tsx
│   └── main-navbar.tsx
├── data/
│   └── ai-tools.ts
├── hooks/
├── lib/
│   ├── ai-providers.ts
│   ├── ai-service.ts
│   ├── audio-utils.ts
│   ├── chat-service.ts
│   ├── database.types
│   ├── db-utils.ts
│   ├── token.ts
│   ├── use-project-data 2.ts
│   ├── use-project-data.ts
│   ├── prompts/
│   │   ├── doc_gen/
│   │   ├── idea/
│   │   ├── outline/
│   │   └── project_details/
├── public/
├── .eslintrc.json
├── .gitattributes
├── .gitconfig
├── .gitignore
├── .gitmessage.txt
├── cache-handler.js
├── chat_implementation_plan.md
├── components.json
├── LICENSE
├── middleware.ts
├── modelrouter.md
├── next.config.mjs
├── package-lock.json
├── package.json
└── postcss.config.mjs
