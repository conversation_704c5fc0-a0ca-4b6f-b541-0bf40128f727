// Test script to validate the documents API fix
// Run with: node test-api-fix.js

const testApiWithoutCookies = async () => {
  const API_URL = 'http://localhost:3000/api/projects/27c45749-436f-485c-adf0-94cb5ca75690/documents';
  
  // Test data - replace with real values
  const testData = {
    selectedDocuments: ['prd'], // Replace with actual document type ID
    projectPlan: 'Test project plan',
    refinedIdea: 'Test idea',
    userId: '2fbff9b7-8492-4068-b80d-df4d9a5d096d' // Replace with real user ID from your database
  };

  try {
    console.log('🧪 Testing documents API without cookies...');
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Deliberately NOT including any cookies or auth headers
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📋 Response Body:`, result);
    
    if (response.status === 200) {
      console.log('✅ SUCCESS: API works without cookies!');
    } else if (response.status === 401) {
      console.log('❌ FAILED: Still getting 401 - cookie dependency remains');
    } else {
      console.log(`⚠️  Unexpected status: ${response.status}`);
    }
    
  } catch (error) {
    console.error('🚨 Error testing API:', error.message);
  }
};

// Run the test
testApiWithoutCookies();